import { describe, it, vi } from 'vitest'
import { vExpect } from 'src/vitest/utils'

import { MAX_SCORE } from './constants'
import type { FetchPeriodGroupScoresData } from './DriverScores/api/queries'
import {
  calculateLegendCounts,
  createScoreBins,
  getColorForScore,
  getScoreRanges,
  toggleItemInSelection,
} from './utils'

// Mock ctIntl
vi.mock('src/util-components/ctIntl', () => ({
  ctIntl: {
    formatMessage: vi.fn(({ id }: { id: string }) => id),
  },
}))

describe('scorecards utils', () => {
  describe('createScoreBins', () => {
    it('should create score bins with correct counts', () => {
      const drivers: FetchPeriodGroupScoresData['drivers'] = [
        {
          id: '1' as any,
          groupIds: [],
          groupNames: [],
          score: 85,
          prevScore: null,
          distanceInKm: 100,
          globalRanking: 1,
        },
        {
          id: '2' as any,
          groupIds: [],
          groupNames: [],
          score: 75,
          prevScore: null,
          distanceInKm: 100,
          globalRanking: 2,
        },
        {
          id: '3' as any,
          groupIds: [],
          groupNames: [],
          score: 85,
          prevScore: null,
          distanceInKm: 100,
          globalRanking: 3,
        },
        {
          id: '4' as any,
          groupIds: [],
          groupNames: [],
          score: null,
          prevScore: null,
          distanceInKm: 100,
          globalRanking: null,
        },
        {
          id: '5' as any,
          groupIds: [],
          groupNames: [],
          score: 90,
          prevScore: null,
          distanceInKm: 100,
          globalRanking: 4,
        },
      ]

      const result = createScoreBins(drivers)

      vExpect(result.length).toBe(MAX_SCORE + 1)
      vExpect(result[75].count).toBe(1)
      vExpect(result[85].count).toBe(2)
      vExpect(result[90].count).toBe(1)
      vExpect(result[0].count).toBe(0) // null scores are not counted
    })

    it('should handle empty drivers array', () => {
      const drivers: FetchPeriodGroupScoresData['drivers'] = []
      const result = createScoreBins(drivers)

      vExpect(result.length).toBe(MAX_SCORE + 1)
      vExpect(result.every((bin) => bin.count === 0)).toBe(true)
    })

    it('should handle drivers with all null scores', () => {
      const drivers: FetchPeriodGroupScoresData['drivers'] = [
        {
          id: '1' as any,
          groupIds: [],
          groupNames: [],
          score: null,
          prevScore: null,
          distanceInKm: 100,
          globalRanking: null,
        },
        {
          id: '2' as any,
          groupIds: [],
          groupNames: [],
          score: null,
          prevScore: null,
          distanceInKm: 100,
          globalRanking: null,
        },
      ]

      const result = createScoreBins(drivers)

      vExpect(result.length).toBe(MAX_SCORE + 1)
      vExpect(result.every((bin) => bin.count === 0)).toBe(true)
    })
  })

  describe('getScoreRanges', () => {
    const mockTheme = {
      palette: {
        error: { main: '#f44336', dark: '#d32f2f' },
        warning: { main: '#ff9800' },
        success: { main: '#4caf50', dark: '#388e3c' },
        primary: { dark: '#1976d2' },
      },
    } as any

    it('should return correct score ranges with default weightage', () => {
      const weightageRange = [40, 75]
      const result = getScoreRanges({ theme: mockTheme, weightageRange })

      vExpect(result.length).toBe(3)

      // Poor range
      vExpect(result[0].min).toBe(0)
      vExpect(result[0].max).toBe(40)
      vExpect(result[0].countKey).toBe('poor')

      // Average range
      vExpect(result[1].min).toBe(41)
      vExpect(result[1].max).toBe(75)
      vExpect(result[1].countKey).toBe('average')

      // Good range
      vExpect(result[2].min).toBe(76)
      vExpect(result[2].max).toBe(MAX_SCORE)
      vExpect(result[2].countKey).toBe('good')
    })

    it('should handle empty weightage range with defaults', () => {
      const weightageRange: Array<number> = []
      const result = getScoreRanges({ theme: mockTheme, weightageRange })

      vExpect(result[0].max).toBe(40) // default first weightage
      vExpect(result[1].max).toBe(75) // default second weightage
    })
  })

  describe('calculateLegendCounts', () => {
    const mockTheme = {
      palette: {
        error: { main: '#f44336', dark: '#d32f2f' },
        warning: { main: '#ff9800' },
        success: { main: '#4caf50', dark: '#388e3c' },
        primary: { dark: '#1976d2' },
      },
    } as any

    it('should calculate legend counts correctly', () => {
      const drivers: FetchPeriodGroupScoresData['drivers'] = [
        {
          id: '1' as any,
          groupIds: [],
          groupNames: [],
          score: 30,
          prevScore: null,
          distanceInKm: 100,
          globalRanking: 1,
        }, // poor
        {
          id: '2' as any,
          groupIds: [],
          groupNames: [],
          score: 50,
          prevScore: null,
          distanceInKm: 100,
          globalRanking: 2,
        }, // average
        {
          id: '3' as any,
          groupIds: [],
          groupNames: [],
          score: 80,
          prevScore: null,
          distanceInKm: 100,
          globalRanking: 3,
        }, // good
        {
          id: '4' as any,
          groupIds: [],
          groupNames: [],
          score: 35,
          prevScore: null,
          distanceInKm: 100,
          globalRanking: 4,
        }, // poor
        {
          id: '5' as any,
          groupIds: [],
          groupNames: [],
          score: null,
          prevScore: null,
          distanceInKm: 100,
          globalRanking: null,
        }, // not counted
      ]
      const weightageRange = [40, 75]

      const result = calculateLegendCounts(drivers, mockTheme, weightageRange)

      vExpect(result.poor).toBe(2)
      vExpect(result.average).toBe(1)
      vExpect(result.good).toBe(1)
    })

    it('should handle drivers with null scores', () => {
      const drivers: FetchPeriodGroupScoresData['drivers'] = [
        {
          id: '1' as any,
          groupIds: [],
          groupNames: [],
          score: null,
          prevScore: null,
          distanceInKm: 100,
          globalRanking: null,
        },
        {
          id: '2' as any,
          groupIds: [],
          groupNames: [],
          score: null,
          prevScore: null,
          distanceInKm: 100,
          globalRanking: null,
        },
      ]
      const weightageRange = [40, 75]

      const result = calculateLegendCounts(drivers, mockTheme, weightageRange)

      vExpect(result.poor).toBe(0)
      vExpect(result.average).toBe(0)
      vExpect(result.good).toBe(0)
    })
  })

  describe('toggleItemInSelection', () => {
    it('should toggle "all" selection correctly', () => {
      const selection = new Set(['item1', 'item2'])
      const result = toggleItemInSelection(selection, 'all')
      vExpect(result).toBe('all')
    })

    it('should toggle from "all" to specific item', () => {
      const selection = 'all'
      const result = toggleItemInSelection(selection, 'item1')
      vExpect(result).toEqual(new Set(['item1']))
    })

    it('should add item to selection', () => {
      const selection = new Set(['item1'])
      const result = toggleItemInSelection(selection, 'item2')
      vExpect(result).toEqual(new Set(['item1', 'item2']))
    })

    it('should remove item from selection', () => {
      const selection = new Set(['item1', 'item2'])
      const result = toggleItemInSelection(selection, 'item1')
      vExpect(result).toEqual(new Set(['item2']))
    })

    it('should toggle "all" from "all" to empty set', () => {
      const selection = 'all'
      const result = toggleItemInSelection(selection, 'all')
      vExpect(result).toEqual(new Set())
    })
  })

  describe('getColorForScore', () => {
    const mockTheme = {
      palette: {
        error: { main: '#f44336', dark: '#d32f2f' },
        warning: { main: '#ff9800' },
        success: { main: '#4caf50', dark: '#388e3c' },
        primary: { dark: '#1976d2' },
        grey: { 600: '#757575', 100: '#f5f5f5', 300: '#e0e0e0' },
      },
    } as any

    it('should return correct color for poor score', () => {
      const score = 30
      const weightageRange = [40, 75]
      const result = getColorForScore(score, mockTheme, weightageRange)

      vExpect(result.color).toBe('#f44336')
      vExpect(result.textColor).toBe('#d32f2f')
    })

    it('should return correct color for average score', () => {
      const score = 50
      const weightageRange = [40, 75]
      const result = getColorForScore(score, mockTheme, weightageRange)

      vExpect(result.color).toBe('#ff9800')
      vExpect(result.textColor).toBe('#1976d2')
    })

    it('should return correct color for good score', () => {
      const score = 80
      const weightageRange = [40, 75]
      const result = getColorForScore(score, mockTheme, weightageRange)

      vExpect(result.color).toBe('#4caf50')
      vExpect(result.textColor).toBe('#388e3c')
    })

    it('should return default color for out of range score', () => {
      const score = -10
      const weightageRange = [40, 75]
      const result = getColorForScore(score, mockTheme, weightageRange)

      vExpect(result.color).toBe('#757575')
      vExpect(result.textColor).toBe('#e0e0e0')
    })
  })
})
