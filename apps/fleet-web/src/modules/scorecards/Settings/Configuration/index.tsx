import { useMemo, useState } from 'react'
import {
  <PERSON>,
  Divider,
  Icon<PERSON>utton,
  KarooFormStateContextProvider,
  Slider,
  Stack,
  styled,
  Switch,
  TextField,
  Tooltip,
  Typography,
  useTheme,
} from '@karoo-ui/core'
import LockOutlineIcon from '@mui/icons-material/LockOutline'
import { useForm, useStore } from '@tanstack/react-form'
import { match } from 'ts-pattern'

import type { ScoreCardScoreCountRuleId } from 'api/types'
import { getVisionSetting } from 'duxs/user'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'
import FormSelection from 'src/util-components/selects/form-select'

import {
  configurationSchema,
  MAX_SCORE,
  type ConfigurationFormType,
} from '../../constants'
import { getScoreRanges } from '../../utils'
import { useUpdateScorecardConfigurationsMutation } from '../api/mutation' // Import the mutation hook
import type { FetchScorecardConfigurationWeightageData } from '../api/queries'
import {
  ConfigurationLine,
  ConfigurationPanel,
  ConfigurationPanelTitlePart,
} from '../components/ConfigurationPanel'
import EditButtonGroup from '../components/EditButtonGroup'
import { useScorecardSettingsContext } from '../ScorecardSettingsContext'
import { formatConfigurationForApi, getConfigurationChangedFields } from '../utils'

const Configuration = ({
  configurationData,
}: {
  configurationData: FetchScorecardConfigurationWeightageData['configurations']
}) => {
  const theme = useTheme()
  const vision = useTypedSelector(getVisionSetting)
  const {
    attributes: { refreshPeriods, conditions },
    defaultValues: { configurationRules: defaultConfigurationRules },
  } = useScorecardSettingsContext()
  const [isEditing, setIsEditing] = useState(false)

  const updateMutation = useUpdateScorecardConfigurationsMutation() // Instantiate the mutation hook

  const minimumRequirementOptions = useMemo(
    () =>
      conditions.map((period) => ({
        value: period.ruleConditionId,
        label: period.ruleCondition,
      })),
    [conditions],
  )

  const refreshPeriodOptions = useMemo(
    () =>
      refreshPeriods.map((period) => ({
        value: period.refreshPeriodId,
        label: period.refreshPeriod,
      })),
    [refreshPeriods],
  )

  const parsedConfigurationData = useMemo(() => {
    const minimumRequirementValue = configurationData.minimumRequirement
    const refreshPeriodValue = configurationData.scorePeriod

    const minimumRequirementValueParsed = conditions
      .map((c) => c.ruleConditionId)
      .includes(minimumRequirementValue.value.id)
      ? minimumRequirementValue
      : {
          value: {
            id: conditions[0].ruleConditionId,
            distance: 5,
            duration: {
              hour: 0,
              minute: 0,
            },
          } as ConfigurationFormType['form']['minimumRequirement']['value'],
        }

    const refreshPeriodValueParsed = refreshPeriods
      .map((c) => c.refreshPeriodId)
      .includes(refreshPeriodValue)
      ? refreshPeriodValue
      : refreshPeriods[0].refreshPeriodId

    return {
      form: {
        ...configurationData,
        refreshPeriodValue: refreshPeriodValueParsed,
        minimumRequirementValue: minimumRequirementValueParsed,
      },
    }
  }, [conditions, refreshPeriods, configurationData])

  const form = useForm({
    validators: { onChange: configurationSchema },
    defaultValues: parsedConfigurationData as ConfigurationFormType,
    onSubmit: ({ value }) => {
      const changed = getConfigurationChangedFields(
        value.form,
        parsedConfigurationData.form,
      )
      const apiInput = formatConfigurationForApi(changed)
      updateMutation.mutate(apiInput, {
        onSuccess: () => {
          setIsEditing(false) // Exit editing mode on successful save
        },
      })
    },
  })

  const isFormValid = useStore(form.store, (state) => state.isValid)
  const isFormDirty = useStore(form.store, (state) => state.isDirty)
  const formValues = useStore(form.store, (state) => state.values)

  return (
    <KarooFormStateContextProvider value={{ readOnly: !isEditing }}>
      <Stack sx={{ padding: 1.5, gap: 2, overflowY: 'auto' }}>
        <EditButtonGroup
          isEditing={isEditing}
          saveButtonDisabled={!isFormValid || !isFormDirty}
          isSaving={updateMutation.isPending}
          onCancelButtonClick={() => {
            form.reset({ form: configurationData }) // Reset form on cancel
            setIsEditing(false)
          }}
          onResetButtonClick={() => {
            form.setFieldValue('form', defaultConfigurationRules) // Reset form on cancel
          }}
          onSaveButtonClick={form.handleSubmit}
          onEditButtonClick={() => setIsEditing(true)}
        />
        <form.Field name="form.aiCameraEventDetection">
          {(field) => (
            <ConfigurationPanel>
              {field.state.value.hasAiCamera === false || vision === false ? (
                <Tooltip
                  title={ctIntl.formatMessage({
                    id: 'scoreCards.settings.configuration.aiCamera.disabledTooltip',
                  })}
                >
                  <IconButton>
                    <LockOutlineIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <Switch
                  disabled={!isEditing}
                  checked={field.state.value.on}
                  onChange={(_, value) =>
                    field.handleChange({ ...field.state.value, on: value })
                  }
                />
              )}

              <ConfigurationPanelTitlePart
                title="scoreCards.settings.configuration.aiCamera.title"
                subtitle="scoreCards.settings.configuration.aiCamera.description"
              />
            </ConfigurationPanel>
          )}
        </form.Field>

        <form.Field name="form.weightageCustomize">
          {(field) => (
            <HorizontalConfigurationPanel>
              <ConfigurationLine sx={{ justifyContent: 'flex-start', gap: 1 }}>
                <Switch
                  disabled={!isEditing}
                  checked={field.state.value.on}
                  onChange={(_, value) =>
                    field.handleChange({ ...field.state.value, on: value })
                  }
                />
                <ConfigurationPanelTitlePart
                  title="scoreCards.settings.configuration.weightageCustomize.title"
                  subtitle="scoreCards.settings.configuration.weightageCustomize.description"
                />
              </ConfigurationLine>
              <Divider flexItem />
              <ConfigurationLine>
                <Box
                  display="flex"
                  justifyContent="flex-start" // Align items to the left
                  alignItems="center"
                  mt={2}
                  gap={3}
                >
                  {getScoreRanges({
                    theme,
                    weightageRange: formValues.form.weightageCustomize.on
                      ? formValues.form.weightageCustomize.range
                      : defaultConfigurationRules.weightageCustomize.range,
                  }).map((range) => (
                    <Box
                      key={range.label}
                      display="flex"
                      alignItems="center"
                    >
                      <Box
                        component="span"
                        sx={{
                          width: 14,
                          height: 14,
                          borderRadius: '50%',
                          backgroundColor: range.color,
                          mr: 1,
                        }}
                      />
                      <Typography
                        color="text.secondary"
                        sx={{ fontWeight: 'bold' }}
                      >
                        {range.label}: {range.min} - {range.max}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </ConfigurationLine>
              {field.state.value.on && (
                <Slider
                  value={field.state.value.range}
                  onChange={(_, rangeValues) =>
                    field.handleChange({
                      ...field.state.value,
                      range: rangeValues as [number, number],
                    })
                  }
                  disabled={!isEditing}
                  valueLabelDisplay="auto"
                  min={0}
                  max={MAX_SCORE}
                  marks={Array.from({ length: 6 }).map((_, index) => {
                    const value = Math.round((index * MAX_SCORE) / 5)
                    return { value, label: value.toString() }
                  })}
                  sx={{
                    height: 5, // Ensure consistent height
                    '& .MuiSlider-thumb': {
                      backgroundColor: 'white',
                    },
                    '& .MuiSlider-track': {
                      // Make the default track transparent as the rail handles colors
                      border: 'none',
                      backgroundColor: 'transparent',
                      height: 5,
                    },
                    '& .MuiSlider-rail': {
                      opacity: 1,
                      height: 5,
                      // Apply the three-color gradient based on thumb positions
                      background: `linear-gradient(to right,
                        ${theme.palette.error.main} 0%,
                        ${theme.palette.error.main} ${
                          (field.state.value.range[0] / MAX_SCORE) * 100
                        }%,
                        ${theme.palette.warning.main} ${
                          (field.state.value.range[0] / MAX_SCORE) * 100
                        }%,
                        ${theme.palette.warning.main} ${
                          (field.state.value.range[1] / MAX_SCORE) * 100
                        }%,
                        ${theme.palette.success.main} ${
                          (field.state.value.range[1] / MAX_SCORE) * 100
                        }%,
                        ${theme.palette.success.main} 100% )`,
                    },
                  }}
                  disableSwap
                />
              )}
            </HorizontalConfigurationPanel>
          )}
        </form.Field>

        <form.Field name="form.scorePeriod">
          {(field) => (
            <HorizontalConfigurationPanel>
              <HorizontalConfigurationLine>
                <ConfigurationPanelTitlePart
                  title="scoreCards.settings.configuration.scorePeriod.title"
                  subtitle="scoreCards.settings.configuration.scorePeriod.description"
                />
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <FormSelection
                    testId="scoreCard-configuration-scorePeriod"
                    value={field.state.value}
                    onChange={(newValue) => {
                      field.handleChange(newValue)
                    }}
                    label="Period"
                    options={refreshPeriodOptions}
                  />
                </Box>
              </HorizontalConfigurationLine>
            </HorizontalConfigurationPanel>
          )}
        </form.Field>

        <form.Field name="form.minimumRequirement">
          {(field) => (
            <HorizontalConfigurationPanel>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '100%',
                  gap: 2,
                }}
              >
                <Switch
                  disabled={!isEditing}
                  checked={field.state.value.on}
                  onChange={(_, value) =>
                    field.handleChange({ ...field.state.value, on: value })
                  }
                />
                <ConfigurationLine>
                  <ConfigurationPanelTitlePart
                    title="scoreCards.settings.configuration.minimumRequirement.title"
                    subtitle="scoreCards.settings.configuration.minimumRequirement.description"
                  />
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <FormSelection
                      testId="scoreCard-configuration-minimumRequirement"
                      value={field.state.value.value.id}
                      onChange={(selectedValue) => {
                        field.handleChange({
                          ...field.state.value,
                          value: {
                            ...field.state.value.value,
                            id: selectedValue,
                          },
                        })
                      }}
                      label="Conditions"
                      options={minimumRequirementOptions}
                    />
                  </Box>
                </ConfigurationLine>
              </Box>
              <Divider flexItem />
              {field.state.value.on &&
                match(field.state.value.value)
                  .with(
                    { id: '1' as ScoreCardScoreCountRuleId },
                    { id: '2' as ScoreCardScoreCountRuleId },
                    { id: '3' as ScoreCardScoreCountRuleId },
                    (value) => (
                      <HorizontalConfigurationLine>
                        <ConfigurationPanelTitlePart
                          title="scoreCards.settings.configuration.minimumRequirement.duration.title"
                          subtitle="scoreCards.settings.configuration.minimumRequirement.duration.description"
                        />
                        {value.duration && (
                          <Stack
                            spacing={2}
                            direction="row"
                          >
                            <TextField
                              type="number"
                              label={ctIntl.formatMessage({ id: 'Time (hours)' })}
                              disabled={!field.state.value.on}
                              value={value.duration.hour}
                              onChange={(e) =>
                                field.handleChange({
                                  ...field.state.value,
                                  value: {
                                    ...value,
                                    duration: {
                                      minute: value.duration?.minute ?? 0,
                                      hour: Number(e.target.value),
                                    },
                                  },
                                })
                              }
                              size="small"
                              sx={{ width: '125px' }}
                              slotProps={{ htmlInput: { min: 0 } }}
                            />
                            <TextField
                              type="number"
                              label={ctIntl.formatMessage({ id: 'Time (mins)' })}
                              disabled={!field.state.value.on}
                              value={value.duration.minute}
                              onChange={(e) =>
                                field.handleChange({
                                  ...field.state.value,
                                  value: {
                                    ...value,
                                    duration: {
                                      hour: value.duration?.hour ?? 0,
                                      minute: Number(e.target.value),
                                    },
                                  },
                                })
                              }
                              size="small"
                              sx={{ width: '125px' }}
                              slotProps={{ htmlInput: { min: 0 } }}
                            />
                          </Stack>
                        )}
                      </HorizontalConfigurationLine>
                    ),
                  )
                  .otherwise(() => null)}
              {field.state.value.on &&
                match(field.state.value.value)
                  .with(
                    { id: '1' as ScoreCardScoreCountRuleId },
                    { id: '2' as ScoreCardScoreCountRuleId },
                    { id: '4' as ScoreCardScoreCountRuleId },
                    (value) => (
                      <HorizontalConfigurationLine>
                        <ConfigurationPanelTitlePart
                          title="scoreCards.settings.configuration.minimumRequirement.distance.title"
                          subtitle="scoreCards.settings.configuration.minimumRequirement.distance.description"
                        />
                        <TextField
                          type="number"
                          label={ctIntl.formatMessage({ id: 'Distance (km)' })}
                          disabled={!field.state.value.on}
                          value={value.distance}
                          onChange={(e) =>
                            field.handleChange({
                              ...field.state.value,
                              value: { ...value, distance: Number(e.target.value) },
                            })
                          }
                          size="small"
                          sx={{ width: '266px' }}
                          slotProps={{ htmlInput: { min: 0 } }}
                        />
                      </HorizontalConfigurationLine>
                    ),
                  )
                  .otherwise(() => null)}
            </HorizontalConfigurationPanel>
          )}
        </form.Field>

        <form.Field name="form.safetyTarget">
          {(field) => (
            <ConfigurationPanel>
              <Switch
                disabled={!isEditing}
                checked={field.state.value.on}
                onChange={(_, value) =>
                  field.handleChange({ ...field.state.value, on: value })
                }
              />
              <ConfigurationLine>
                <ConfigurationPanelTitlePart
                  title="scoreCards.settings.configuration.safetyTarget.title"
                  subtitle="scoreCards.settings.configuration.safetyTarget.description"
                />
                <TextField
                  type="number"
                  label={ctIntl.formatMessage({
                    id: 'scoreCards.settings.configuration.safetyTarget.inputTitle',
                  })}
                  disabled={!field.state.value.on}
                  value={field.state.value.value}
                  onChange={(e) =>
                    field.handleChange({
                      ...field.state.value,
                      value: Number(e.target.value),
                    })
                  }
                  size="small"
                  sx={{ width: '180px' }}
                  slotProps={{ htmlInput: { min: 1, max: MAX_SCORE } }}
                />
              </ConfigurationLine>
            </ConfigurationPanel>
          )}
        </form.Field>

        <form.Field name="form.speedingGracePeriod">
          {(field) => (
            <ConfigurationPanel>
              <Switch
                disabled={!isEditing}
                checked={field.state.value.on}
                onChange={(_, value) =>
                  field.handleChange({ ...field.state.value, on: value })
                }
              />
              <ConfigurationLine>
                <ConfigurationPanelTitlePart
                  title="scoreCards.settings.configuration.speedingGracePeriod.title"
                  subtitle="scoreCards.settings.configuration.speedingGracePeriod.description"
                />
                <TextField
                  type="number"
                  label={ctIntl.formatMessage({
                    id: 'scoreCards.settings.configuration.speedingGracePeriod.inputTitle',
                  })}
                  disabled={!field.state.value.on}
                  value={field.state.value.value}
                  onChange={(e) =>
                    field.handleChange({
                      ...field.state.value,
                      value: Number(e.target.value),
                    })
                  }
                  size="small"
                  sx={{ width: '180px' }}
                  slotProps={{ htmlInput: { min: 1 } }}
                />
              </ConfigurationLine>
            </ConfigurationPanel>
          )}
        </form.Field>
      </Stack>
    </KarooFormStateContextProvider>
  )
}

const HorizontalConfigurationPanel = styled(ConfigurationPanel)({
  flexDirection: 'column',
  alignItems: 'flex-start',
})

const HorizontalConfigurationLine = styled(ConfigurationLine)({
  width: '100%',
})

export default Configuration
