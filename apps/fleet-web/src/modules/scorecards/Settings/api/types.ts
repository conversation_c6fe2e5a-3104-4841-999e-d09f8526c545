import { z } from 'zod/v4'

import { MAX_SCORE, type scorecardRawWeightageApiKeys } from '../../constants'

export type ConfigurationRawType = {
  ai_camera_event_detection: {
    enabled: boolean
    has_ai_camera?: boolean // has vehicle with vision
  }
  customize_performance_bands: {
    enabled: boolean
    values: Array<number> //[Poor max, Average max]
  }
  default_scoring_period: {
    refresh_period_id: number | `${number}`
  }
  minimum_requirement: {
    enabled: boolean
    rule_condition_id: number | `${number}`
    distance?: number // indicates the km
    duration?: number // indicate minutes
  }
  safety_score_target: {
    enabled: boolean
    value: number
  }
  speeding_grace_period: {
    enabled: boolean
    value: number
  }
}

type ScorecardRawWeightageKeys = typeof scorecardRawWeightageApiKeys

export type ScorecardRawWeightages = {
  [key in ScorecardRawWeightageKeys[number]]: WeightageRaw
}

export declare namespace FetchScorecardConfigurationWeightage {
  type ApiOutput = {
    configuration_rules: ConfigurationRawType
    weightage_rules: ScorecardRawWeightages
  }
}

export declare namespace ScorecardConfigurationsMutate {
  type ApiInput = {
    ai_camera_event_detection?: Partial<
      ConfigurationRawType['ai_camera_event_detection']
    >
    customize_performance_bands?: Partial<
      ConfigurationRawType['customize_performance_bands']
    >
    default_scoring_period?: Partial<ConfigurationRawType['default_scoring_period']>
    minimum_requirement?: Partial<ConfigurationRawType['minimum_requirement']>
    safety_score_target?: Partial<ConfigurationRawType['safety_score_target']>
    speeding_grace_period?: Partial<ConfigurationRawType['speeding_grace_period']>
  }

  type ApiOutput = ConfigurationRawType
}

export declare namespace ScorecardWeightageMutate {
  type ApiInput = Partial<ScorecardRawWeightages>
  type ApiOutput = ScorecardRawWeightages
}

export const weightageRawSchema = z.object({
  enabled: z.boolean(),
  weightage_level_id: z.number().min(0),
  custom: z.number().min(0).max(MAX_SCORE),
})

// Type definition based on the image
export type WeightageRaw = z.infer<typeof weightageRawSchema>

// Namespace for ct_fleet_scorecard_get_configuration_attributes
export declare namespace FetchScorecardConfigurationAttributes {
  type ApiOutput = {
    conditions: Array<{
      rule_condition_id: number
      rule_condition: string
      translation_id: string
    }>
    refresh_periods: Array<{
      refresh_period_id: number
      refresh_period: string
      translation_id: string
    }>
    weightage_levels: Array<{
      weightage_level_id: number
      weightage_level: string
      value: number | null
      translation_id: string
    }>
  }
}
