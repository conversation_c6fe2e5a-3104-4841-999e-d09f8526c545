import { describe, it, vi } from 'vitest'
import { vExpect } from 'src/vitest/utils'

import {
  parseScorecardConfiguration,
  parseScorecardWeightage,
} from './queries'

// Mock dependencies
vi.mock('api/api-caller', () => ({
  apiCallerNoX: vi.fn(),
}))

vi.mock('api/helpers', () => ({
  makeQueryErrorHandlerWithToast: vi.fn(() => ({})),
}))

vi.mock('src/util-functions/react-query-utils', () => ({
  createQuery: vi.fn(),
}))

vi.mock('src/util-functions/functional-utils', () => ({
  minutesToMs: vi.fn((minutes: number) => minutes * 60 * 1000),
}))

vi.mock('../../constants', () => ({
  weightageApiKeyToFormPathMap: [
    { apiKey: 'speeding_10', formPath: 'speeding_10', label: 'Speeding 10+' },
    { apiKey: 'harsh_braking', formPath: 'harsh_braking', label: 'Harsh Braking' },
  ],
}))

describe('Settings API queries parsing functions', () => {
  describe('parseScorecardConfiguration', () => {
    it('should parse AI camera event detection correctly', () => {
      const mockApiData = {
        ai_camera_event_detection: {
          enabled: true,
          has_ai_camera: false,
        },
        customize_performance_bands: {
          enabled: false,
          values: [40, 75],
        },
        default_scoring_period: {
          refresh_period_id: 2,
        },
        minimum_requirement: {
          enabled: false,
          rule_condition_id: 1,
          distance: 100,
          duration: 120, // 2 hours in minutes
        },
        safety_score_target: {
          enabled: true,
          value: 75,
        },
        speeding_grace_period: {
          enabled: false,
          value: 5,
        },
      }

      const result = parseScorecardConfiguration(mockApiData)

      vExpect(result.aiCameraEventDetection).toEqual({
        on: true,
        hasAiCamera: false,
      })
    })

    it('should parse weightage customize correctly', () => {
      const mockApiData = {
        ai_camera_event_detection: { enabled: false, has_ai_camera: false },
        customize_performance_bands: {
          enabled: true,
          values: [30, 80],
        },
        default_scoring_period: { refresh_period_id: 1 },
        minimum_requirement: { enabled: false, rule_condition_id: 1 },
        safety_score_target: { enabled: false, value: 50 },
        speeding_grace_period: { enabled: false, value: 0 },
      }

      const result = parseScorecardConfiguration(mockApiData)

      vExpect(result.weightageCustomize).toEqual({
        on: true,
        range: [30, 80],
      })
    })

    it('should parse score period correctly', () => {
      const mockApiData = {
        ai_camera_event_detection: { enabled: false, has_ai_camera: false },
        customize_performance_bands: { enabled: false, values: [40, 75] },
        default_scoring_period: {
          refresh_period_id: 3,
        },
        minimum_requirement: { enabled: false, rule_condition_id: 1 },
        safety_score_target: { enabled: false, value: 50 },
        speeding_grace_period: { enabled: false, value: 0 },
      }

      const result = parseScorecardConfiguration(mockApiData)

      vExpect(result.scorePeriod).toBe('3')
    })

    it('should parse minimum requirement with duration correctly', () => {
      const mockApiData = {
        ai_camera_event_detection: { enabled: false, has_ai_camera: false },
        customize_performance_bands: { enabled: false, values: [40, 75] },
        default_scoring_period: { refresh_period_id: 1 },
        minimum_requirement: {
          enabled: true,
          rule_condition_id: 2,
          distance: 150,
          duration: 150, // 2.5 hours in minutes
        },
        safety_score_target: { enabled: false, value: 50 },
        speeding_grace_period: { enabled: false, value: 0 },
      }

      const result = parseScorecardConfiguration(mockApiData)

      vExpect(result.minimumRequirement).toEqual({
        on: true,
        value: {
          id: '2',
          distance: 150,
          duration: {
            hour: 2,
            minute: 30,
          },
        },
      })
    })

    it('should parse minimum requirement without duration correctly', () => {
      const mockApiData = {
        ai_camera_event_detection: { enabled: false, has_ai_camera: false },
        customize_performance_bands: { enabled: false, values: [40, 75] },
        default_scoring_period: { refresh_period_id: 1 },
        minimum_requirement: {
          enabled: true,
          rule_condition_id: 1,
          distance: 100,
          // no duration field
        },
        safety_score_target: { enabled: false, value: 50 },
        speeding_grace_period: { enabled: false, value: 0 },
      }

      const result = parseScorecardConfiguration(mockApiData)

      vExpect(result.minimumRequirement).toEqual({
        on: true,
        value: {
          id: '1',
          distance: 100,
          // no duration field should be present
        },
      })
    })

    it('should parse safety target correctly', () => {
      const mockApiData = {
        ai_camera_event_detection: { enabled: false, has_ai_camera: false },
        customize_performance_bands: { enabled: false, values: [40, 75] },
        default_scoring_period: { refresh_period_id: 1 },
        minimum_requirement: { enabled: false, rule_condition_id: 1 },
        safety_score_target: {
          enabled: true,
          value: 85,
        },
        speeding_grace_period: { enabled: false, value: 0 },
      }

      const result = parseScorecardConfiguration(mockApiData)

      vExpect(result.safetyTarget).toEqual({
        on: true,
        value: 85,
      })
    })

    it('should parse speeding grace period correctly', () => {
      const mockApiData = {
        ai_camera_event_detection: { enabled: false, has_ai_camera: false },
        customize_performance_bands: { enabled: false, values: [40, 75] },
        default_scoring_period: { refresh_period_id: 1 },
        minimum_requirement: { enabled: false, rule_condition_id: 1 },
        safety_score_target: { enabled: false, value: 50 },
        speeding_grace_period: {
          enabled: true,
          value: 10,
        },
      }

      const result = parseScorecardConfiguration(mockApiData)

      vExpect(result.speedingGracePeriod).toEqual({
        on: true,
        value: 10,
      })
    })
  })

  describe('parseScorecardWeightage', () => {
    it('should parse weightage data correctly', () => {
      const mockWeightageRawData = {
        speeding_10: {
          enabled: true,
          weightage_level_id: 2,
          custom: 15,
        },
        harsh_braking: {
          enabled: false,
          weightage_level_id: 1,
          custom: 0,
        },
      }

      const result = parseScorecardWeightage(mockWeightageRawData)

      vExpect(result.speeding_10).toEqual({
        enabled: true,
        weightageId: '2',
        custom: 15,
      })

      vExpect(result.harsh_braking).toEqual({
        enabled: false,
        weightageId: '1',
        custom: 0,
      })
    })

    it('should handle invalid weightage data with fallback', () => {
      // Mock console.error to avoid noise in tests
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      const mockWeightageRawData = {
        speeding_10: {
          // missing required fields to trigger validation error
          invalid: 'data',
        },
      }

      const result = parseScorecardWeightage(mockWeightageRawData)

      // Should fall back to default weightage
      vExpect(result.speeding_10).toEqual({
        enabled: true,
        weightageId: '3',
        custom: 0,
      })

      vExpect(consoleSpy).toHaveBeenCalledWith(
        'Error parsing weightage:',
        expect.any(Object),
      )

      consoleSpy.mockRestore()
    })

    it('should handle empty weightage data', () => {
      const mockWeightageRawData = {}

      const result = parseScorecardWeightage(mockWeightageRawData)

      // Should return empty object since no mappings match
      vExpect(Object.keys(result)).toHaveLength(0)
    })
  })

  describe('Edge cases and error handling', () => {
    it('should handle null values in configuration', () => {
      const mockApiData = {
        ai_camera_event_detection: { enabled: false, has_ai_camera: null },
        customize_performance_bands: { enabled: false, values: null },
        default_scoring_period: { refresh_period_id: null },
        minimum_requirement: {
          enabled: false,
          rule_condition_id: null,
          distance: null,
          duration: null,
        },
        safety_score_target: { enabled: false, value: null },
        speeding_grace_period: { enabled: false, value: null },
      }

      // Should not throw errors
      const result = parseScorecardConfiguration(mockApiData)

      vExpect(result.aiCameraEventDetection.hasAiCamera).toBe(null)
      vExpect(result.weightageCustomize.range).toBe(null)
    })

    it('should handle zero duration in minimum requirement', () => {
      const mockApiData = {
        ai_camera_event_detection: { enabled: false, has_ai_camera: false },
        customize_performance_bands: { enabled: false, values: [40, 75] },
        default_scoring_period: { refresh_period_id: 1 },
        minimum_requirement: {
          enabled: true,
          rule_condition_id: 1,
          distance: 100,
          duration: 0,
        },
        safety_score_target: { enabled: false, value: 50 },
        speeding_grace_period: { enabled: false, value: 0 },
      }

      const result = parseScorecardConfiguration(mockApiData)

      vExpect(result.minimumRequirement.value.duration).toEqual({
        hour: 0,
        minute: 0,
      })
    })

    it('should handle large duration values correctly', () => {
      const mockApiData = {
        ai_camera_event_detection: { enabled: false, has_ai_camera: false },
        customize_performance_bands: { enabled: false, values: [40, 75] },
        default_scoring_period: { refresh_period_id: 1 },
        minimum_requirement: {
          enabled: true,
          rule_condition_id: 1,
          distance: 100,
          duration: 1440, // 24 hours in minutes
        },
        safety_score_target: { enabled: false, value: 50 },
        speeding_grace_period: { enabled: false, value: 0 },
      }

      const result = parseScorecardConfiguration(mockApiData)

      vExpect(result.minimumRequirement.value.duration).toEqual({
        hour: 24,
        minute: 0,
      })
    })
  })
})
