import { useQuery } from '@tanstack/react-query'

import { apiCallerNoX } from 'api/api-caller'
import { makeQueryErrorHandlerWithToast } from 'api/helpers'
import type {
  ScoreCardRefreshPeriodId,
  ScoreCardScoreCountRuleId,
  ScoreCardWeightageId,
} from 'api/types'
import type { PromiseResolvedType } from 'src/types'
import { minutesToMs } from 'src/util-functions/functional-utils'
import { createQuery } from 'src/util-functions/react-query-utils'

import {
  weightageApiKeyToFormPathMap,
  type WeightageConfigType,
  type WeightageValue,
} from '../../constants'
import { handleBETranslationId } from '../utils'
import {
  weightageRawSchema,
  type ConfigurationRawType,
  type FetchScorecardConfigurationAttributes,
  type FetchScorecardConfigurationWeightage,
  type ScorecardRawWeightages,
  type WeightageRaw,
} from './types'

const defaultWeightage = {
  enabled: true,
  weightageId: '3' as const,
  custom: 0,
} as WeightageValue

// Parser function to transform API output to Zod schema structure
export function parseScorecardConfiguration(apiData: ConfigurationRawType) {
  const rawMinimumRequirement = apiData.minimum_requirement

  const parsedData = {
    aiCameraEventDetection: {
      on: apiData.ai_camera_event_detection.enabled,
      hasAiCamera: apiData.ai_camera_event_detection.has_ai_camera ?? false, // maps to has_ai_camera
    },
    weightageCustomize: {
      on: apiData.customize_performance_bands.enabled,
      range: apiData.customize_performance_bands.values as [number, number],
    },
    scorePeriod: apiData.default_scoring_period
      .refresh_period_id as ScoreCardRefreshPeriodId,
    minimumRequirement: {
      on: rawMinimumRequirement.enabled,
      value: {
        id: `${rawMinimumRequirement.rule_condition_id}` as ScoreCardScoreCountRuleId,
        distance: rawMinimumRequirement.distance,
        ...(rawMinimumRequirement.duration
          ? {
              duration: {
                hour: Math.floor(rawMinimumRequirement.duration / 60),
                minute: rawMinimumRequirement.duration % 60,
              },
            }
          : {}),
      },
    },
    safetyTarget: {
      on: apiData.safety_score_target.enabled,
      value: apiData.safety_score_target.value,
    },
    speedingGracePeriod: {
      on: apiData.speeding_grace_period.enabled,
      value: apiData.speeding_grace_period.value,
    },
  }

  return parsedData
}

export const parseScorecardWeightage = (weightageRawData: ScorecardRawWeightages) =>
  weightageApiKeyToFormPathMap.reduce(
    (acc, { apiKey, formPath }) => ({
      ...acc,
      [formPath]: parseWeightage(weightageRawData[apiKey]),
    }),
    {} as Record<keyof WeightageConfigType['form'], WeightageValue>,
  )

const parseWeightage = (weightage: WeightageRaw): WeightageValue => {
  const parsedResult = weightageRawSchema.safeParse(weightage)

  if (!parsedResult.success) {
    console.error('Error parsing weightage:', parsedResult.error)
    return defaultWeightage
  }

  return {
    enabled: parsedResult.data.enabled,
    weightageId: `${parsedResult.data.weightage_level_id}` as ScoreCardWeightageId,
    custom: parsedResult.data.custom,
  }
}

// Async function to fetch configurations
async function fetchScorecardConfigurationWeightage() {
  const result = await apiCallerNoX<FetchScorecardConfigurationWeightage.ApiOutput>(
    'ct_fleet_scorecard_get_user_configuration',
  )

  // Parse and validate the result before returning
  return {
    weightageData: parseScorecardWeightage(result.weightage_rules),
    configurations: parseScorecardConfiguration(result.configuration_rules),
  }
}

// React Query query function
export function scorecardConfigurationWeightageQuery() {
  return createQuery({
    queryKey: ['scorecards/fetchConfigurations'] as const,
    queryFn: fetchScorecardConfigurationWeightage,
    staleTime: minutesToMs(5),
    ...makeQueryErrorHandlerWithToast(),
  })
}

// Custom hook for using the query
export function useScorecardConfigurationWeightageQuery() {
  return useQuery(scorecardConfigurationWeightageQuery())
}

// Type helper for the data returned by the hook
export type FetchScorecardConfigurationWeightageData = PromiseResolvedType<
  typeof fetchScorecardConfigurationWeightage
>

async function fetchScorecardConfigurationAttributes() {
  const rawData = await apiCallerNoX<FetchScorecardConfigurationAttributes.ApiOutput>(
    'ct_fleet_scorecard_get_configuration_attributes',
  )

  return {
    conditions: rawData.conditions.map((condition) => ({
      ruleConditionId: `${condition.rule_condition_id}` as ScoreCardScoreCountRuleId,
      ruleCondition: handleBETranslationId({
        translationId: condition.translation_id,
        value: condition.rule_condition,
      }),
    })),
    refreshPeriods: rawData.refresh_periods.map((period) => ({
      refreshPeriodId: `${period.refresh_period_id}` as ScoreCardRefreshPeriodId,
      refreshPeriod: handleBETranslationId({
        translationId: period.translation_id,
        value: period.refresh_period,
      }),
    })),
    weightageLevels: rawData.weightage_levels
      .map((level) => ({
        weightageLevelId: `${level.weightage_level_id}` as ScoreCardWeightageId,
        label: handleBETranslationId({
          translationId: level.translation_id,
          value: level.weightage_level,
        }),
        value: level.weightage_level.toLocaleLowerCase(),
        summaryMsgId: handleBETranslationId({
          translationId: level.translation_id + '.summary',
          value: level.weightage_level,
        }),
        threshold: level.value,
      }))
      .sort((a, b) => {
        if (!a.threshold) {
          return 1
        }
        if (!b.threshold) {
          return -1
        }
        return a.threshold - b.threshold
      }),
  }
}

export const scorecardConfigurationAttributesQuery = () =>
  createQuery({
    queryKey: ['scorecardConfigurationAttributes'] as const,
    queryFn: fetchScorecardConfigurationAttributes,
    staleTime: minutesToMs(5), // Adjust staleTime as needed
    ...makeQueryErrorHandlerWithToast(),
  })

export function useScorecardConfigurationAttributesQuery() {
  return useQuery(scorecardConfigurationAttributesQuery())
}

export type FetchScorecardConfigurationAttributesResolved = PromiseResolvedType<
  typeof fetchScorecardConfigurationAttributes
>

async function fetchScorecardDefaultConfigurationWeightage() {
  const rawData = await apiCallerNoX<FetchScorecardConfigurationWeightage.ApiOutput>(
    'ct_fleet_scorecard_get_default_configuration',
  )

  const weightageRawData = rawData.weightage_rules

  return {
    configurationRules: parseScorecardConfiguration(rawData.configuration_rules),
    weightageRules: parseScorecardWeightage(weightageRawData),
  }
}

export const scorecardDefaultConfigurationWeightageQuery = () =>
  createQuery({
    queryKey: ['scorecardDefaultConfigurationWeightage'] as const,
    queryFn: fetchScorecardDefaultConfigurationWeightage,
    staleTime: minutesToMs(10), // Adjust staleTime as needed
    ...makeQueryErrorHandlerWithToast(),
  })

export function useScorecardDefaultConfigurationWeightageQuery() {
  return useQuery(scorecardDefaultConfigurationWeightageQuery())
}

export type FetchScorecardDefaultConfigurationWeightageResolved = PromiseResolvedType<
  typeof fetchScorecardDefaultConfigurationWeightage
>
