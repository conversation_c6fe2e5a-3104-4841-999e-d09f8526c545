import { describe, expect, it, vi } from 'vitest'
import { vExpect } from 'src/vitest/utils'

import type { ConfigurationFormType, WeightageConfigType } from '../constants'
import {
  compareObjects,
  formatConfigurationForApi,
  formatWeightageForApi,
  getConfigurationChangedFields,
  getWeightageChangedFields,
  handleBETranslationId,
} from './utils'

// Mock dependencies
vi.mock('src/util-components/ctIntl', () => ({
  ctIntl: {
    formatMessage: vi.fn(({ id }: { id: string }) => {
      if (id === 'valid.translation.key') return 'Translated Text'
      return id // Return the key itself if no translation found
    }),
  },
}))

vi.mock('src/util-functions/string-utils', () => ({
  isNilOrEmptyString: vi.fn((str: string) => !str || str.trim() === ''),
}))

describe('Settings utils', () => {
  describe('handleBETranslationId', () => {
    it('should return translated text when translation exists', () => {
      const result = handleBETranslationId({
        translationId: 'valid.translation.key',
        value: 'Original Value',
      })

      vExpect(result).toBe('Translated Text')
    })

    it('should return original value when translation does not exist', () => {
      const result = handleBETranslationId({
        translationId: 'invalid.translation.key',
        value: 'Original Value',
      })

      vExpect(result).toBe('Original Value')
    })

    it('should return original value when translationId is empty', () => {
      const result = handleBETranslationId({
        translationId: '',
        value: 'Original Value',
      })

      vExpect(result).toBe('Original Value')
    })
  })

  describe('compareObjects', () => {
    it('should return true for identical primitive values', () => {
      vExpect(compareObjects(1, 1)).toBe(true)
      vExpect(compareObjects('test', 'test')).toBe(true)
      vExpect(compareObjects(true, true)).toBe(true)
      vExpect(compareObjects(null, null)).toBe(true)
      vExpect(compareObjects(undefined, undefined)).toBe(true)
    })

    it('should return false for different primitive values', () => {
      vExpect(compareObjects(1, 2)).toBe(false)
      vExpect(compareObjects('test', 'other')).toBe(false)
      vExpect(compareObjects(true, false)).toBe(false)
      vExpect(compareObjects(null, undefined)).toBe(false)
    })

    it('should return true for identical objects', () => {
      const obj1 = { a: 1, b: 'test', c: { nested: true } }
      const obj2 = { a: 1, b: 'test', c: { nested: true } }

      vExpect(compareObjects(obj1, obj2)).toBe(true)
    })

    it('should return false for different objects', () => {
      const obj1 = { a: 1, b: 'test' }
      const obj2 = { a: 1, b: 'different' }

      vExpect(compareObjects(obj1, obj2)).toBe(false)
    })

    it('should return false for objects with different keys', () => {
      const obj1 = { a: 1, b: 'test' }
      const obj2 = { a: 1, c: 'test' }

      vExpect(compareObjects(obj1, obj2)).toBe(false)
    })

    it('should handle nested objects correctly', () => {
      const obj1 = { a: { b: { c: 1 } } }
      const obj2 = { a: { b: { c: 1 } } }
      const obj3 = { a: { b: { c: 2 } } }

      vExpect(compareObjects(obj1, obj2)).toBe(true)
      vExpect(compareObjects(obj1, obj3)).toBe(false)
    })

    it('should handle arrays correctly', () => {
      const arr1 = [1, 2, 3]
      const arr2 = [1, 2, 3]
      const arr3 = [1, 2, 4]

      vExpect(compareObjects(arr1, arr2)).toBe(true)
      vExpect(compareObjects(arr1, arr3)).toBe(false)
    })
  })

  describe('getWeightageChangedFields', () => {
    const mockFormData: WeightageConfigType['form'] = {
      speeding_10: { enabled: true, weightageId: '1' as any, custom: 5 },
      speeding_20: { enabled: false, weightageId: '2' as any, custom: 0 },
      harsh_braking: { enabled: true, weightageId: '1' as any, custom: 0 },
    } as any

    const mockInitialData: WeightageConfigType['form'] = {
      speeding_10: { enabled: true, weightageId: '1' as any, custom: 5 },
      speeding_20: { enabled: true, weightageId: '2' as any, custom: 0 }, // changed
      harsh_braking: { enabled: false, weightageId: '2' as any, custom: 10 }, // changed
    } as any

    it('should identify changed fields correctly', () => {
      const result = getWeightageChangedFields(mockFormData, mockInitialData)

      vExpect(Object.keys(result)).toEqual(['speeding_20', 'harsh_braking'])
      vExpect(Object.keys(result)).notToBe(['speeding_10'])
      vExpect(Object.keys(result)).notToBe([])
    })

    it('should return empty object when no changes', () => {
      const result = getWeightageChangedFields(mockFormData, mockFormData)

      vExpect(Object.keys(result).length).toBe(0)
    })
  })

  describe('getConfigurationChangedFields', () => {
    const mockFormData: Partial<ConfigurationFormType['form']> = {
      aiCameraEventDetection: { on: true, hasAiCamera: false },
      scorePeriod: '2' as any,
    }

    const mockInitialData: ConfigurationFormType['form'] = {
      aiCameraEventDetection: { on: false, hasAiCamera: false }, // changed
      scorePeriod: '2' as any,
      weightageCustomize: { on: false, range: [40, 75] },
      minimumRequirement: { on: false, value: { id: '1' as any } },
      safetyTarget: { on: false, value: 50 },
      speedingGracePeriod: { on: false, value: 5 },
    }

    it('should identify changed fields correctly', () => {
      const result = getConfigurationChangedFields(mockFormData, mockInitialData)

      expect(Object.keys(result)).toContain('aiCameraEventDetection')
      expect(Object.keys(result)).not.toBe(['scorePeriod'])
    })

    it('should return empty object when no changes', () => {
      const partialData = {
        aiCameraEventDetection: { on: false, hasAiCamera: false },
        scorePeriod: '2' as any,
      }

      const result = getConfigurationChangedFields(partialData, mockInitialData)

      vExpect(Object.keys(result).length).toBe(4)
    })
  })

  describe('formatWeightageForApi', () => {
    it('should format weightage data for API correctly', () => {
      const changedFields: Partial<WeightageConfigType['form']> = {
        speeding_10: { enabled: true, weightageId: '1' as any, custom: 5 },
        harsh_braking: { enabled: false, weightageId: '2' as any, custom: 0 },
      } as any

      const result = formatWeightageForApi(changedFields)

      // The exact structure depends on weightageApiKeyToFormPathMap
      vExpect(typeof result).toBe('object')
    })

    it('should handle empty changed fields', () => {
      const result = formatWeightageForApi({})

      vExpect(typeof result).toBe('object')
      vExpect(Object.keys(result).length).toBe(0)
    })
  })

  describe('formatConfigurationForApi', () => {
    it('should format AI camera event detection correctly', () => {
      const formData: Partial<ConfigurationFormType['form']> = {
        aiCameraEventDetection: { on: true, hasAiCamera: false },
      }

      const result = formatConfigurationForApi(formData)

      vExpect(result.ai_camera_event_detection).toEqual({
        enabled: true,
        has_ai_camera: false,
      })
    })

    it('should format weightage customize correctly', () => {
      const formData: Partial<ConfigurationFormType['form']> = {
        weightageCustomize: { on: true, range: [30, 80] },
      }

      const result = formatConfigurationForApi(formData)

      vExpect(result.customize_performance_bands).toEqual({
        enabled: true,
        values: [30, 80],
      })
    })

    it('should format score period correctly', () => {
      const formData: Partial<ConfigurationFormType['form']> = {
        scorePeriod: '3' as any,
      }

      const result = formatConfigurationForApi(formData)

      vExpect(result.default_scoring_period).toEqual({
        refresh_period_id: 3,
      })
    })

    it('should format minimum requirement correctly', () => {
      const formData: Partial<ConfigurationFormType['form']> = {
        minimumRequirement: {
          on: true,
          value: {
            id: '2' as any,
            distance: 100,
            duration: { hour: 2, minute: 30 },
          },
        },
      }

      const result = formatConfigurationForApi(formData)

      vExpect(result.minimum_requirement).toEqual({
        enabled: true,
        rule_condition_id: 2,
        distance: 100,
        duration: 150, // 2 hours * 60 + 30 minutes
      })
    })

    it('should format safety target correctly', () => {
      const formData: Partial<ConfigurationFormType['form']> = {
        safetyTarget: { on: true, value: 75 },
      }

      const result = formatConfigurationForApi(formData)

      vExpect(result.safety_score_target).toEqual({
        enabled: true,
        value: 75,
      })
    })

    it('should format speeding grace period correctly', () => {
      const formData: Partial<ConfigurationFormType['form']> = {
        speedingGracePeriod: { on: true, value: 10 },
      }

      const result = formatConfigurationForApi(formData)

      vExpect(result.speeding_grace_period).toEqual({
        enabled: true,
        value: 10,
      })
    })

    it('should handle empty form data', () => {
      const result = formatConfigurationForApi({})

      vExpect(typeof result).toBe('object')
      vExpect(Object.keys(result).length).toBe(0)
    })
  })
})
