import { useCallback, useMemo, useRef, useState } from 'react'
import { Box, colors, Stack, Typography, useTheme } from '@karoo-ui/core'
import { AxisBottom, AxisLeft } from '@visx/axis'
import { localPoint } from '@visx/event'
import { GridRows } from '@visx/grid'
import { Group } from '@visx/group'
import { scaleLinear, scaleTime } from '@visx/scale'
import { LinePath } from '@visx/shape'
import { defaultStyles, TooltipWithBounds, useTooltip } from '@visx/tooltip'
import { DateTime } from 'luxon'

import { ctIntl } from 'src/util-components/ctIntl'

import { MAX_SCORE } from '../../constants'
import { MARGIN } from '../../utils'
import { SectionWithTitle } from '../components/SectionWithTitle'
import useChartResize from '../hooks/useChartResize'
import type { DataPoint, LineSeries, ScoreOverviewData } from '../Overview/types'
import { bisectDate } from '../utils'
import { ScoreChangeIndicator } from './ScoreChangeIndicator'

type ScoreOverviewChartProps = {
  data: ScoreOverviewData
}

type TooltipData = {
  point: DataPoint
  activeSeries: string
}

export const ScoreOverviewChart = ({ data }: ScoreOverviewChartProps) => {
  const theme = useTheme()
  const containerRef = useRef<HTMLDivElement | null>(null)
  const [dimensions, setDimensions] = useState({ width: 700, height: 300 })

  useChartResize({ containerRef, setDimensions })

  const xMax = dimensions.width - MARGIN.left - MARGIN.right
  const yMax = dimensions.height - MARGIN.top - MARGIN.bottom

  const xScale = useMemo(
    () =>
      scaleTime<number>({
        domain: [data.dateRange.start, data.dateRange.end],
        range: [0, xMax],
      }),
    [xMax, data.dateRange.start, data.dateRange.end],
  )

  const yScale = useMemo(
    () =>
      scaleLinear<number>({
        domain: [0, MAX_SCORE],
        range: [yMax, 0],
        nice: true,
      }),
    [yMax],
  )

  const { showTooltip, hideTooltip, tooltipData, tooltipTop, tooltipLeft } =
    useTooltip<TooltipData>()

  const formatDate = useCallback(
    (date: Date) => DateTime.fromJSDate(date).toLocaleString(DateTime.DATE_MED),
    [],
  )

  const handleMouseMove = useCallback(
    (event: React.TouchEvent<SVGRectElement> | React.MouseEvent<SVGRectElement>) => {
      const svgPoint = localPoint(event) || { x: 0, y: 0 }
      const x0 = xScale.invert(svgPoint.x - MARGIN.left)

      const sortedData = [...data.dailyScores].sort(
        (a, b) => a.date.getTime() - b.date.getTime(),
      )
      const index = bisectDate(sortedData, x0)

      if (index < 0 || index >= sortedData.length) {
        hideTooltip()
        return
      }

      const d0 = sortedData[index]
      const d1 = sortedData[index + 1]

      let dataPoint = d0
      if (d1 && x0.valueOf() - d0.date.valueOf() > d1.date.valueOf() - x0.valueOf()) {
        dataPoint = d1
      }

      // Calculate which line is closest to the mouse Y position
      const mouseYValue = yScale.invert(svgPoint.y - MARGIN.top)

      // Find the series with the value closest to mouseYValue
      let closestSeries = data.lineSeries[0].key
      let closestDistance = Math.abs(
        (dataPoint[data.lineSeries[0].key] as number) - mouseYValue,
      )

      for (const series of data.lineSeries) {
        const distance = Math.abs((dataPoint[series.key] as number) - mouseYValue)
        if (distance < closestDistance) {
          closestDistance = distance
          closestSeries = series.key
        }
      }

      // if exceed the threshold, do not show tooltip
      if (closestDistance < 20) {
        showTooltip({
          tooltipData: {
            point: dataPoint,
            activeSeries: closestSeries,
          },
          tooltipLeft: svgPoint.x,
          tooltipTop: svgPoint.y,
        })
      } else {
        hideTooltip()
      }
    },
    [data.dailyScores, data.lineSeries, showTooltip, hideTooltip, xScale, yScale],
  )

  const generateLinePathWithDashedSegments = useCallback(
    (series: LineSeries) => {
      // Find continuous segments of non-null data
      const dailyScores = data.dailyScores
      const nonNullSegments: Array<Array<DataPoint>> = []
      let tempSegment: Array<DataPoint> = []

      // Create segments of continuous data
      for (let index = 0; index < dailyScores.length; index++) {
        const point = dailyScores[index]
        if (point[series.key] !== null) {
          tempSegment.push(point)
        } else if (tempSegment.length > 0) {
          nonNullSegments.push([...tempSegment])
          tempSegment = []
        }

        // Add the last segment if we're at the end
        if (index === dailyScores.length - 1 && tempSegment.length > 0) {
          nonNullSegments.push([...tempSegment])
        }
      }

      // Create dashed segments to connect gaps
      const dashedSegments: Array<[DataPoint, DataPoint]> = []
      for (let i = 0; i < nonNullSegments.length - 1; i++) {
        const lastPointOfSegment = nonNullSegments[i][nonNullSegments[i].length - 1]
        const firstPointOfNextSegment = nonNullSegments[i + 1][0]
        dashedSegments.push([lastPointOfSegment, firstPointOfNextSegment])
      }

      const isActive = tooltipData && tooltipData.activeSeries === series.key
      let opacity = 1
      if (isActive) {
        opacity = 1
      } else if (tooltipData) {
        opacity = 0.8
      }
      const strokeWidth = isActive
        ? (series.strokeWidth || 1.5) + 0.5
        : series.strokeWidth || 1.5

      return (
        <g key={series.key}>
          {/* Render solid line segments */}
          {nonNullSegments.map((segment, i) => (
            <LinePath
              key={`${series.key}-segment-${i}`}
              data={segment}
              x={(d) => xScale(d.date)}
              y={(d) => yScale(d[series.key] as number)}
              stroke={series.color}
              strokeWidth={strokeWidth}
              strokeDasharray={series.dashed ? '5,5' : undefined}
              curve={series.curve}
              opacity={opacity}
            />
          ))}

          {/* Render dashed lines for gaps */}
          {dashedSegments.map((segment, i) => (
            <LinePath
              key={`${series.key}-dashed-${i}`}
              data={segment}
              x={(d) => xScale(d.date)}
              y={(d) => yScale(d[series.key] as number)}
              stroke={series.color}
              strokeWidth={strokeWidth}
              strokeDasharray="5,5"
              curve={series.curve}
              opacity={opacity * 0.5}
            />
          ))}
        </g>
      )
    },
    [data.dailyScores, tooltipData, xScale, yScale],
  )

  return (
    <SectionWithTitle
      titleMsgId="scoreCards.driverScores.chart.scoreOverview"
      dateRange={[
        DateTime.fromJSDate(data.dateRange.start),
        DateTime.fromJSDate(data.dateRange.end),
      ]}
    >
      <Box
        display="flex"
        justifyContent="flex-start"
        alignItems="flex-start"
        mt={1}
        gap={20}
      >
        <Box>
          <Typography
            variant="caption"
            color="text.secondary"
          >
            {ctIntl.formatMessage({
              id: 'scoreCards.driverScores.chart.periods.current',
            })}
          </Typography>
          <Box
            display="flex"
            alignItems="center"
          >
            <Typography
              variant="h3"
              color="success.main"
              fontWeight="bold"
              sx={{ mr: 1 }}
            >
              {data.currentPeriod.score}
            </Typography>
            <ScoreChangeIndicator
              {...data.currentPeriod.changed}
              reverse
              renderScore={(value) =>
                ctIntl.formatMessage(
                  { id: 'scoreCards.driverScores.changeValue' },
                  { values: { value } },
                )
              }
            />
          </Box>
        </Box>

        <Box>
          <Typography
            variant="caption"
            color="text.secondary"
          >
            {ctIntl.formatMessage({
              id: 'scoreCards.driverScores.chart.periods.previous',
            })}
          </Typography>
          <Typography
            variant="h3"
            color="text.secondary"
            fontWeight="bold"
          >
            {data.previousPeriod.score}
          </Typography>
        </Box>
      </Box>

      <Box
        ref={containerRef}
        height={dimensions.height}
        position="relative"
      >
        <svg
          width={dimensions.width}
          height={dimensions.height}
        >
          <Group
            left={MARGIN.left}
            top={MARGIN.top}
          >
            <GridRows
              scale={yScale}
              width={xMax}
              height={yMax}
              stroke={theme.palette.divider}
              strokeOpacity={0.2}
              numTicks={5}
            />
            <AxisBottom
              top={yMax}
              scale={xScale}
              numTicks={Math.min(5, data.dailyScores.length)}
              tickFormat={(d) => formatDate(d as Date)}
              stroke={theme.palette.text.secondary}
              tickStroke={theme.palette.text.secondary}
              tickLabelProps={{
                fill: theme.palette.text.secondary,
                fontSize: 10,
                textAnchor: 'middle',
              }}
            />
            <AxisLeft
              scale={yScale}
              stroke={theme.palette.text.secondary}
              tickStroke={theme.palette.text.secondary}
              tickLabelProps={{
                fill: theme.palette.text.secondary,
                fontSize: 10,
                textAnchor: 'end',
                dx: '-0.25em',
                dy: '0.25em',
              }}
            />
            {data.lineSeries.map((series) => {
              if (series.key === 'safetyScore') {
                return (
                  <LinePath
                    key={series.key}
                    data={data.dailyScores.filter(
                      (score) => score[series.key] !== null,
                    )}
                    x={(score) => xScale(score.date)}
                    y={(score) => yScale(score[series.key] as number)}
                    stroke={series.color}
                    strokeWidth={
                      tooltipData && tooltipData.activeSeries === series.key
                        ? (series.strokeWidth || 1.5) + 0.5
                        : series.strokeWidth || 1.5
                    }
                    strokeDasharray="5,5"
                    curve={series.curve}
                    opacity={
                      tooltipData && tooltipData.activeSeries !== series.key ? 0.8 : 1
                    }
                  />
                )
              }

              return generateLinePathWithDashedSegments(series)
            })}
            <rect
              x={0}
              y={0}
              width={xMax}
              height={yMax}
              fill="transparent"
              onMouseMove={handleMouseMove}
              onMouseLeave={hideTooltip}
            />
          </Group>
        </svg>

        {tooltipData &&
          tooltipData.point[tooltipData.activeSeries] !== null &&
          tooltipLeft != null &&
          tooltipTop != null && (
            <TooltipWithBounds
              top={tooltipTop}
              left={tooltipLeft}
              style={{
                ...defaultStyles,
                backgroundColor: theme.palette.grey[600],
                color: 'white',
                whiteSpace: 'nowrap',
              }}
            >
              <Typography>{formatDate(tooltipData.point.date)}</Typography>
              {data.lineSeries
                .filter((series) => series.key === tooltipData.activeSeries)
                .map((series) => (
                  <Stack
                    key={series.key}
                    direction="row"
                    alignItems="center"
                    gap={1}
                  >
                    <Typography>
                      {series.label}
                      {`:`}
                    </Typography>
                    <Box
                      sx={({ palette }) => ({
                        py: 0.5,
                        px: 1.25,
                        bgcolor: colors.orange[50],
                        color: palette.primary.dark,
                        borderRadius: '4px',
                      })}
                    >
                      {tooltipData.point[series.key] as number}
                    </Box>
                  </Stack>
                ))}
            </TooltipWithBounds>
          )}
      </Box>

      <Box
        display="flex"
        justifyContent="space-between"
        flexDirection="column"
        gap={2}
        mt={3}
      >
        <Box
          display="flex"
          alignItems="center"
        >
          <Box mr={4}>
            <Typography color="text.secondary">
              {ctIntl.formatMessage({
                id: 'scoreCards.driverScores.chart.metrics.distanceDriven',
              })}
            </Typography>
            <Typography
              variant="body1"
              fontWeight="medium"
            >
              {ctIntl.formatMessage(
                { id: 'units.kilometersWithValue' },
                { values: { value: data.distanceDriven.toLocaleString() } },
              )}
            </Typography>
          </Box>

          <Box>
            <Typography color="text.secondary">
              {ctIntl.formatMessage({
                id: 'scoreCards.driverScores.chart.metrics.timeDriven',
              })}
            </Typography>
            <Typography
              variant="body1"
              fontWeight="medium"
            >
              {ctIntl.formatMessage(
                { id: 'units.timeFormat' },
                {
                  values: {
                    hours: data.timeDriven.hours,
                    minutes: data.timeDriven.minutes,
                  },
                },
              )}
            </Typography>
          </Box>
        </Box>

        <Box
          display="flex"
          alignItems="center"
          sx={({ palette }) => ({
            width: 'fit-content',
            py: 1,
            px: 2,
            backgroundColor: palette.grey[50],
            borderRadius: 1,
          })}
        >
          {data.lineSeries.map((series, index) => (
            <Box
              key={series.key}
              display="flex"
              alignItems="center"
              mr={index < data.lineSeries.length - 1 ? 2 : 0}
            >
              <Box
                component="span"
                sx={{
                  display: 'inline-block',
                  width: 10,
                  height: 10,
                  borderRadius: '50%',
                  backgroundColor: series.color,
                  mr: 1,
                }}
              />
              <Typography>{series.label}</Typography>
            </Box>
          ))}
        </Box>
      </Box>
    </SectionWithTitle>
  )
}
