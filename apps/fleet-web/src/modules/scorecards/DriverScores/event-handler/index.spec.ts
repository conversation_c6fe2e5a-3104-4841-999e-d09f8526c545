import { beforeEach, describe, expect, it, vi } from 'vitest'
import { vExpect } from 'src/vitest/utils'

import {
  COMPARISON_GROUP_COLORS,
  driverScoresEventHandler,
  generateComparisonGroupUUID,
  MAX_COMPARISON_GROUPS,
} from './index'
import type { DriverScoresEvent, DriverScoresState } from './types'

// Mock uuid
vi.mock('uuid', () => ({
  v4: vi.fn(() => 'mock-uuid-1234'),
}))

describe('DriverScores event handler', () => {
  const createMockState = (
    overrides: Partial<DriverScoresState> = {},
  ): DriverScoresState => ({
    comparisonGroups: [],
    nextComparisonGroupNumber: 1,
    editingComparisonGroupMeta: null,
    ...overrides,
  })

  const mockSetState = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('generateComparisonGroupUUID', () => {
    it('should generate a UUID', () => {
      const result = generateComparisonGroupUUID()
      vExpect(result).toBe('mock-uuid-1234')
    })
  })

  describe('add_comparison_group_button__onClick', () => {
    it('should add a new comparison group', () => {
      const state = createMockState()
      const event: DriverScoresEvent = { type: 'add_comparison_group_button__onClick' }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      expect(mockSetState).toHaveBeenCalledWith({
        comparisonGroups: [
          {
            id: 'mock-uuid-1234',
            name: 'Group 1',
            driverGroupIds: new Set(),
            vehicleGroupIds: new Set(),
            vehicleTypeId: null,
            color: COMPARISON_GROUP_COLORS[0],
          },
        ],
        nextComparisonGroupNumber: 2,
      })
    })

    it('should not add group when max groups reached', () => {
      const state = createMockState({
        comparisonGroups: new Array(MAX_COMPARISON_GROUPS).fill({
          id: 'existing-group',
          name: 'Existing Group',
          driverGroupIds: new Set(),
          vehicleGroupIds: new Set(),
          vehicleTypeId: null,
          color: '#000000',
        }),
      })
      const event: DriverScoresEvent = { type: 'add_comparison_group_button__onClick' }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      expect(mockSetState).not.toHaveBeenCalled()
    })

    it('should use available color when some colors are used', () => {
      const state = createMockState({
        comparisonGroups: [
          {
            id: 'group1',
            name: 'Group 1',
            driverGroupIds: new Set(),
            vehicleGroupIds: new Set(),
            vehicleTypeId: null,
            color: COMPARISON_GROUP_COLORS[0],
          },
        ],
        nextComparisonGroupNumber: 2,
      })
      const event: DriverScoresEvent = { type: 'add_comparison_group_button__onClick' }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      const newGroup = mockSetState.mock.calls[0][0].comparisonGroups[1]
      vExpect(newGroup.color).toBe(COMPARISON_GROUP_COLORS[1])
    })

    it('should cycle through colors when all are used', () => {
      const state = createMockState({
        comparisonGroups: COMPARISON_GROUP_COLORS.map((color, index) => ({
          id: `group${index}`,
          name: `Group ${index}`,
          driverGroupIds: new Set(),
          vehicleGroupIds: new Set(),
          vehicleTypeId: null,
          color,
        })),
        nextComparisonGroupNumber: COMPARISON_GROUP_COLORS.length + 1,
      })
      const event: DriverScoresEvent = { type: 'add_comparison_group_button__onClick' }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      const newGroup =
        mockSetState.mock.calls[0][0].comparisonGroups[COMPARISON_GROUP_COLORS.length]
      vExpect(newGroup.color).toBe(COMPARISON_GROUP_COLORS[0]) // Should cycle back to first color
    })
  })

  describe('comparison_group_remove_icon_button__onClick', () => {
    it('should remove the specified group', () => {
      const state = createMockState({
        comparisonGroups: [
          {
            id: 'group1',
            name: 'Group 1',
            driverGroupIds: new Set(),
            vehicleGroupIds: new Set(),
            vehicleTypeId: null,
            color: '#000000',
          },
          {
            id: 'group2',
            name: 'Group 2',
            driverGroupIds: new Set(),
            vehicleGroupIds: new Set(),
            vehicleTypeId: null,
            color: '#111111',
          },
        ],
      })
      const event: DriverScoresEvent = {
        type: 'comparison_group_remove_icon_button__onClick',
        groupId: 'group1',
      }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      expect(mockSetState).toHaveBeenCalledWith({
        comparisonGroups: [
          {
            id: 'group2',
            name: 'Group 2',
            driverGroupIds: new Set(),
            vehicleGroupIds: new Set(),
            vehicleTypeId: null,
            color: '#111111',
          },
        ],
        editingComparisonGroupMeta: null,
      })
    })

    it('should clear editing meta if removing the group being edited', () => {
      const state = createMockState({
        comparisonGroups: [
          {
            id: 'group1',
            name: 'Group 1',
            driverGroupIds: new Set(),
            vehicleGroupIds: new Set(),
            vehicleTypeId: null,
            color: '#000000',
          },
        ],
        editingComparisonGroupMeta: { id: 'group1', name: 'Group 1' },
      })
      const event: DriverScoresEvent = {
        type: 'comparison_group_remove_icon_button__onClick',
        groupId: 'group1',
      }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      vExpect(mockSetState.mock.calls[0][0].editingComparisonGroupMeta).toBe(null)
    })

    it('should preserve editing meta if removing different group', () => {
      const editingMeta = { id: 'group2', name: 'Group 2' }
      const state = createMockState({
        comparisonGroups: [
          {
            id: 'group1',
            name: 'Group 1',
            driverGroupIds: new Set(),
            vehicleGroupIds: new Set(),
            vehicleTypeId: null,
            color: '#000000',
          },
          {
            id: 'group2',
            name: 'Group 2',
            driverGroupIds: new Set(),
            vehicleGroupIds: new Set(),
            vehicleTypeId: null,
            color: '#111111',
          },
        ],
        editingComparisonGroupMeta: editingMeta,
      })
      const event: DriverScoresEvent = {
        type: 'comparison_group_remove_icon_button__onClick',
        groupId: 'group1',
      }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      vExpect(mockSetState.mock.calls[0][0].editingComparisonGroupMeta).toBe(
        editingMeta,
      )
    })
  })

  describe('driver_and_vehicle_groups_picker__onVehicleDriverGroupIdsChange', () => {
    it('should update group vehicle and driver group IDs', () => {
      const state = createMockState({
        comparisonGroups: [
          {
            id: 'group1',
            name: 'Group 1',
            driverGroupIds: new Set(['old-driver-group']),
            vehicleGroupIds: new Set(['old-vehicle-group']),
            vehicleTypeId: null,
            color: '#000000',
          },
        ],
      })
      const event: DriverScoresEvent = {
        type: 'driver_and_vehicle_groups_picker__onVehicleDriverGroupIdsChange',
        payload: {
          id: 'group1',
          vehicleGroupIds: new Set(['new-vehicle-group']),
          driverGroupIds: new Set(['new-driver-group']),
        },
      }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      expect(mockSetState).toHaveBeenCalledWith({
        comparisonGroups: [
          {
            id: 'group1',
            name: 'Group 1',
            driverGroupIds: new Set(['new-driver-group']),
            vehicleGroupIds: new Set(['new-vehicle-group']),
            vehicleTypeId: null,
            color: '#000000',
          },
        ],
      })
    })

    it('should only update the specified group', () => {
      const unchangedGroup = {
        id: 'group2',
        name: 'Group 2',
        driverGroupIds: new Set(['unchanged-driver']),
        vehicleGroupIds: new Set(['unchanged-vehicle']),
        vehicleTypeId: null,
        color: '#111111',
      }

      const state = createMockState({
        comparisonGroups: [
          {
            id: 'group1',
            name: 'Group 1',
            driverGroupIds: new Set(['old-driver-group']),
            vehicleGroupIds: new Set(['old-vehicle-group']),
            vehicleTypeId: null,
            color: '#000000',
          },
          unchangedGroup,
        ],
      })
      const event: DriverScoresEvent = {
        type: 'driver_and_vehicle_groups_picker__onVehicleDriverGroupIdsChange',
        payload: {
          id: 'group1',
          vehicleGroupIds: new Set(['new-vehicle-group']),
          driverGroupIds: new Set(['new-driver-group']),
        },
      }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      const updatedGroups = mockSetState.mock.calls[0][0].comparisonGroups
      vExpect(updatedGroups[1]).toBe(unchangedGroup) // Should be the same reference
    })
  })

  describe('comparison_group_name__click', () => {
    it('should set editing meta for the clicked group', () => {
      const state = createMockState({
        comparisonGroups: [
          {
            id: 'group1',
            name: 'Test Group',
            driverGroupIds: new Set(),
            vehicleGroupIds: new Set(),
            vehicleTypeId: null,
            color: '#000000',
          },
        ],
      })
      const event: DriverScoresEvent = {
        type: 'comparison_group_name__click',
        groupId: 'group1',
      }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      expect(mockSetState).toHaveBeenCalledWith({
        editingComparisonGroupMeta: {
          id: 'group1',
          name: 'Test Group',
        },
      })
    })

    it('should handle non-existent group', () => {
      const state = createMockState({
        comparisonGroups: [],
      })
      const event: DriverScoresEvent = {
        type: 'comparison_group_name__click',
        groupId: 'non-existent',
      }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      expect(mockSetState).toHaveBeenCalledWith({
        editingComparisonGroupMeta: {
          id: 'non-existent',
          name: '',
        },
      })
    })
  })

  describe('comparison_group_editing_name_textfield__onChange', () => {
    it('should update editing meta name', () => {
      const state = createMockState({
        editingComparisonGroupMeta: { id: 'group1', name: 'Old Name' },
      })
      const mockEvent = { target: { value: 'New Name' } } as any
      const event: DriverScoresEvent = {
        type: 'comparison_group_editing_name_textfield__onChange',
        domEvent: mockEvent,
      }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      expect(mockSetState).toHaveBeenCalledWith({
        editingComparisonGroupMeta: {
          id: 'group1',
          name: 'New Name',
        },
      })
    })

    it('should do nothing when no editing meta', () => {
      const state = createMockState({
        editingComparisonGroupMeta: null,
      })
      const mockEvent = { target: { value: 'New Name' } } as any
      const event: DriverScoresEvent = {
        type: 'comparison_group_editing_name_textfield__onChange',
        domEvent: mockEvent,
      }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      vExpect(mockSetState).not.toHaveBeenCalled()
    })
  })

  describe('comparison_group_editing_name_textfield__onBlur', () => {
    it('should persist name and exit edit mode when name is not empty', () => {
      const state = createMockState({
        comparisonGroups: [
          {
            id: 'group1',
            name: 'Old Name',
            driverGroupIds: new Set(),
            vehicleGroupIds: new Set(),
            vehicleTypeId: null,
            color: '#000000',
          },
        ],
        editingComparisonGroupMeta: { id: 'group1', name: '  New Name  ' },
      })
      const event: DriverScoresEvent = {
        type: 'comparison_group_editing_name_textfield__onBlur',
      }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      expect(mockSetState).toHaveBeenCalledWith({
        comparisonGroups: [
          {
            id: 'group1',
            name: 'New Name', // Should be trimmed
            driverGroupIds: new Set(),
            vehicleGroupIds: new Set(),
            vehicleTypeId: null,
            color: '#000000',
          },
        ],
        editingComparisonGroupMeta: null,
      })
    })

    it('should exit edit mode without persisting when name is empty', () => {
      const state = createMockState({
        comparisonGroups: [
          {
            id: 'group1',
            name: 'Old Name',
            driverGroupIds: new Set(),
            vehicleGroupIds: new Set(),
            vehicleTypeId: null,
            color: '#000000',
          },
        ],
        editingComparisonGroupMeta: { id: 'group1', name: '   ' },
      })
      const event: DriverScoresEvent = {
        type: 'comparison_group_editing_name_textfield__onBlur',
      }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      expect(mockSetState).toHaveBeenCalledWith({
        editingComparisonGroupMeta: null,
      })
    })

    it('should do nothing when no editing meta', () => {
      const state = createMockState({
        editingComparisonGroupMeta: null,
      })
      const event: DriverScoresEvent = {
        type: 'comparison_group_editing_name_textfield__onBlur',
      }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      vExpect(mockSetState).toHaveBeenCalledWith({
        editingComparisonGroupMeta: null,
      })
    })
  })

  describe('comparison_group_editing_name_textfield__onKeyDown', () => {
    it('should persist name on Enter key', () => {
      const state = createMockState({
        comparisonGroups: [
          {
            id: 'group1',
            name: 'Old Name',
            driverGroupIds: new Set(),
            vehicleGroupIds: new Set(),
            vehicleTypeId: null,
            color: '#000000',
          },
        ],
        editingComparisonGroupMeta: { id: 'group1', name: 'New Name' },
      })
      const mockEvent = { key: 'Enter' } as any
      const event: DriverScoresEvent = {
        type: 'comparison_group_editing_name_textfield__onKeyDown',
        domEvent: mockEvent,
      }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      vExpect(mockSetState).toHaveBeenCalledWith({
        comparisonGroups: [
          {
            id: 'group1',
            name: 'New Name',
            driverGroupIds: new Set(),
            vehicleGroupIds: new Set(),
            vehicleTypeId: null,
            color: '#000000',
          },
        ],
        editingComparisonGroupMeta: null,
      })
    })

    it('should do nothing on other keys', () => {
      const state = createMockState({
        editingComparisonGroupMeta: { id: 'group1', name: 'New Name' },
      })
      const mockEvent = { key: 'Escape' } as any
      const event: DriverScoresEvent = {
        type: 'comparison_group_editing_name_textfield__onKeyDown',
        domEvent: mockEvent,
      }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      vExpect(mockSetState).not.toHaveBeenCalled()
    })

    it('should do nothing when no editing meta', () => {
      const state = createMockState({
        editingComparisonGroupMeta: null,
      })
      const mockEvent = { key: 'Enter' } as any
      const event: DriverScoresEvent = {
        type: 'comparison_group_editing_name_textfield__onKeyDown',
        domEvent: mockEvent,
      }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      vExpect(mockSetState).not.toHaveBeenCalled()
    })
  })

  describe('comparison_group_selected_type_autocomplete__onChange', () => {
    it('should update vehicle type ID', () => {
      const state = createMockState({
        comparisonGroups: [
          {
            id: 'group1',
            name: 'Group 1',
            driverGroupIds: new Set(),
            vehicleGroupIds: new Set(),
            vehicleTypeId: null,
            color: '#000000',
          },
        ],
      })
      const event: DriverScoresEvent = {
        type: 'comparison_group_selected_type_autocomplete__onChange',
        groupId: 'group1',
        option: { value: 'vehicle-type-123' },
      }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      vExpect(mockSetState).toHaveBeenCalledWith({
        comparisonGroups: [
          {
            id: 'group1',
            name: 'Group 1',
            driverGroupIds: new Set(),
            vehicleGroupIds: new Set(),
            vehicleTypeId: 'vehicle-type-123',
            color: '#000000',
          },
        ],
      })
    })

    it('should clear vehicle type ID when option is null', () => {
      const state = createMockState({
        comparisonGroups: [
          {
            id: 'group1',
            name: 'Group 1',
            driverGroupIds: new Set(),
            vehicleGroupIds: new Set(),
            vehicleTypeId: 'existing-type',
            color: '#000000',
          },
        ],
      })
      const event: DriverScoresEvent = {
        type: 'comparison_group_selected_type_autocomplete__onChange',
        groupId: 'group1',
        option: null,
      }

      driverScoresEventHandler({ state, event, setState: mockSetState })

      vExpect(mockSetState).toHaveBeenCalledWith({
        comparisonGroups: [
          {
            id: 'group1',
            name: 'Group 1',
            driverGroupIds: new Set(),
            vehicleGroupIds: new Set(),
            vehicleTypeId: null,
            color: '#000000',
          },
        ],
      })
    })
  })
})
