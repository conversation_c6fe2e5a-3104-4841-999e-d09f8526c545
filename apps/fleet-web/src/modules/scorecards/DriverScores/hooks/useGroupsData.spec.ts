import { beforeEach, describe, it, vi } from 'vitest'
import { renderHook } from '@testing-library/react'
import { useDriversQuery } from 'src/modules/api/useDriversQuery'
import { useVehiclesQuery } from 'src/modules/api/useVehiclesQuery'
import { vExpect } from 'src/vitest/utils'

import { useGroupsData } from './useGroupsData'

// Mock the API hooks
vi.mock('src/modules/api/useDriversQuery', () => ({
  useDriversQuery: vi.fn(),
}))

vi.mock('src/modules/api/useVehiclesQuery', () => ({
  useVehiclesQuery: vi.fn(),
}))

vi.mock('src/util-components/ctIntl', () => ({
  ctIntl: {
    formatMessage: vi.fn(({ id }: { id: string }) => {
      if (id === 'All Drivers') return 'All Drivers'
      if (id === 'All Vehicles') return 'All Vehicles'
      return id
    }),
  },
}))

const mockUseDriversQuery = useDriversQuery as any
const mockUseVehiclesQuery = useVehiclesQuery as any

describe('useGroupsData', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('driverGroups', () => {
    it('should return all drivers option when no driver groups data', () => {
      mockUseDriversQuery.mockReturnValue({
        data: {
          activeDrivers: [{ id: '1' }, { id: '2' }],
          driverGroupsMapById: null,
        },
      })
      mockUseVehiclesQuery.mockReturnValue({ data: null })

      const { result } = renderHook(() => useGroupsData())

      vExpect(result.current.driverGroups).toEqual([
        {
          id: 'all',
          name: 'All Drivers',
          count: 2,
        },
      ])
    })

    it('should return all drivers option plus driver groups', () => {
      const mockDriverGroupsMap = new Map([
        [
          'group1',
          {
            id: 'group1',
            name: 'Group 1',
            itemIds: ['driver1', 'driver2'],
          },
        ],
        [
          'group2',
          {
            id: 'group2',
            name: 'Group 2',
            itemIds: ['driver3'],
          },
        ],
      ])

      mockUseDriversQuery.mockReturnValue({
        data: {
          activeDrivers: [{ id: '1' }, { id: '2' }, { id: '3' }],
          driverGroupsMapById: mockDriverGroupsMap,
        },
      })
      mockUseVehiclesQuery.mockReturnValue({ data: null })

      const { result } = renderHook(() => useGroupsData())

      vExpect(result.current.driverGroups).toEqual([
        {
          id: 'all',
          name: 'All Drivers',
          count: 3,
        },
        {
          id: 'group1',
          name: 'Group 1',
          count: 2,
        },
        {
          id: 'group2',
          name: 'Group 2',
          count: 1,
        },
      ])
    })

    it('should handle driver groups without itemIds', () => {
      const mockDriverGroupsMap = new Map([
        [
          'group1',
          {
            id: 'group1',
            name: 'Group 1',
            itemIds: null,
          },
        ],
      ])

      mockUseDriversQuery.mockReturnValue({
        data: {
          activeDrivers: [{ id: '1' }],
          driverGroupsMapById: mockDriverGroupsMap,
        },
      })
      mockUseVehiclesQuery.mockReturnValue({ data: null })

      const { result } = renderHook(() => useGroupsData())

      vExpect(result.current.driverGroups[1]).toEqual({
        id: 'group1',
        name: 'Group 1',
        count: 0,
      })
    })
  })

  describe('vehicleGroups', () => {
    it('should return all vehicles option when no vehicle groups data', () => {
      mockUseDriversQuery.mockReturnValue({ data: null })
      mockUseVehiclesQuery.mockReturnValue({
        data: {
          vehicles: [{ id: '1' }, { id: '2' }],
          vehicleGroups: null,
        },
      })

      const { result } = renderHook(() => useGroupsData())

      vExpect(result.current.vehicleGroups).toEqual([
        {
          id: 'all',
          name: 'All Vehicles',
          count: 2,
        },
      ])
    })

    it('should return all vehicles option plus vehicle groups', () => {
      mockUseDriversQuery.mockReturnValue({ data: null })
      mockUseVehiclesQuery.mockReturnValue({
        data: {
          vehicles: [{ id: '1' }, { id: '2' }, { id: '3' }],
          vehicleGroups: [
            {
              id: 'vgroup1',
              name: 'Vehicle Group 1',
              itemIdsSet: new Set(['vehicle1', 'vehicle2']),
            },
            {
              id: 'vgroup2',
              name: 'Vehicle Group 2',
              itemIdsSet: new Set(['vehicle3']),
            },
          ],
        },
      })

      const { result } = renderHook(() => useGroupsData())

      vExpect(result.current.vehicleGroups).toEqual([
        {
          id: 'all',
          name: 'All Vehicles',
          count: 3,
        },
        {
          id: 'vgroup1',
          name: 'Vehicle Group 1',
          count: 2,
        },
        {
          id: 'vgroup2',
          name: 'Vehicle Group 2',
          count: 1,
        },
      ])
    })

    it('should handle vehicle groups without itemIdsSet', () => {
      mockUseDriversQuery.mockReturnValue({ data: null })
      mockUseVehiclesQuery.mockReturnValue({
        data: {
          vehicles: [{ id: '1' }],
          vehicleGroups: [
            {
              id: 'vgroup1',
              name: 'Vehicle Group 1',
              itemIdsSet: null,
            },
          ],
        },
      })

      const { result } = renderHook(() => useGroupsData())

      vExpect(result.current.vehicleGroups[1]).toEqual({
        id: 'vgroup1',
        name: 'Vehicle Group 1',
        count: 0,
      })
    })
  })

  describe('getDriverGroupName', () => {
    it('should return correct driver group name', () => {
      const mockDriverGroupsMap = new Map([
        [
          'group1',
          {
            id: 'group1',
            name: 'Test Driver Group',
            itemIds: [],
          },
        ],
      ])

      mockUseDriversQuery.mockReturnValue({
        data: {
          activeDrivers: [],
          driverGroupsMapById: mockDriverGroupsMap,
        },
      })
      mockUseVehiclesQuery.mockReturnValue({ data: null })

      const { result } = renderHook(() => useGroupsData())

      vExpect(result.current.getDriverGroupName('group1' as any)).toBe(
        'Test Driver Group',
      )
    })

    it('should return empty string for non-existent group', () => {
      mockUseDriversQuery.mockReturnValue({
        data: {
          activeDrivers: [],
          driverGroupsMapById: new Map(),
        },
      })
      mockUseVehiclesQuery.mockReturnValue({ data: null })

      const { result } = renderHook(() => useGroupsData())

      vExpect(result.current.getDriverGroupName('nonexistent' as any)).toBe('')
    })

    it('should return empty string when no driver groups data', () => {
      mockUseDriversQuery.mockReturnValue({
        data: {
          activeDrivers: [],
          driverGroupsMapById: null,
        },
      })
      mockUseVehiclesQuery.mockReturnValue({ data: null })

      const { result } = renderHook(() => useGroupsData())

      vExpect(result.current.getDriverGroupName('group1' as any)).toBe('')
    })
  })

  describe('getVehicleGroupName', () => {
    it('should return correct vehicle group name', () => {
      const mockVehicleGroupsMap = new Map([
        [
          'vgroup1',
          {
            id: 'vgroup1',
            name: 'Test Vehicle Group',
          },
        ],
      ])

      mockUseDriversQuery.mockReturnValue({ data: null })
      mockUseVehiclesQuery.mockReturnValue({
        data: {
          vehicles: [],
          vehicleGroupsById: mockVehicleGroupsMap,
        },
      })

      const { result } = renderHook(() => useGroupsData())

      vExpect(result.current.getVehicleGroupName('vgroup1' as any)).toBe(
        'Test Vehicle Group',
      )
    })

    it('should return empty string for non-existent group', () => {
      mockUseDriversQuery.mockReturnValue({ data: null })
      mockUseVehiclesQuery.mockReturnValue({
        data: {
          vehicles: [],
          vehicleGroupsById: new Map(),
        },
      })

      const { result } = renderHook(() => useGroupsData())

      vExpect(result.current.getVehicleGroupName('nonexistent' as any)).toBe('')
    })

    it('should return empty string when no vehicle groups data', () => {
      mockUseDriversQuery.mockReturnValue({ data: null })
      mockUseVehiclesQuery.mockReturnValue({
        data: {
          vehicles: [],
          vehicleGroupsById: null,
        },
      })

      const { result } = renderHook(() => useGroupsData())

      vExpect(result.current.getVehicleGroupName('vgroup1' as any)).toBe('')
    })
  })

  describe('edge cases', () => {
    it('should handle null data gracefully', () => {
      mockUseDriversQuery.mockReturnValue({ data: null })
      mockUseVehiclesQuery.mockReturnValue({ data: null })

      const { result } = renderHook(() => useGroupsData())

      vExpect(result.current.driverGroups).toEqual([
        {
          id: 'all',
          name: 'All Drivers',
          count: 0,
        },
      ])

      vExpect(result.current.vehicleGroups).toEqual([
        {
          id: 'all',
          name: 'All Vehicles',
          count: 0,
        },
      ])
    })

    it('should handle undefined activeDrivers and vehicles', () => {
      mockUseDriversQuery.mockReturnValue({
        data: {
          activeDrivers: undefined,
          driverGroupsMapById: null,
        },
      })
      mockUseVehiclesQuery.mockReturnValue({
        data: {
          vehicles: undefined,
          vehicleGroups: null,
        },
      })

      const { result } = renderHook(() => useGroupsData())

      vExpect(result.current.driverGroups[0].count).toBe(0)
      vExpect(result.current.vehicleGroups[0].count).toBe(0)
    })
  })
})
