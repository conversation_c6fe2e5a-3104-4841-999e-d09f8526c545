import { afterEach, beforeEach, describe, it, vi } from 'vitest'
import { renderHook } from '@testing-library/react'
import { vExpect } from 'src/vitest/utils'

import useChartResize from './useChartResize'

// Mock ResizeObserver
class MockResizeObserver {
  private callback: ResizeObserverCallback
  private elements: Set<Element> = new Set()

  constructor(callback: ResizeObserverCallback) {
    this.callback = callback
  }

  observe(element: Element) {
    this.elements.add(element)
  }

  unobserve(element: Element) {
    this.elements.delete(element)
  }

  disconnect() {
    this.elements.clear()
  }

  // Helper method to trigger resize
  triggerResize(entries: Array<ResizeObserverEntry>) {
    this.callback(entries, this)
  }
}

// describe('useChartResize', () => {
//   let mockResizeObserver: MockResizeObserver
//   let originalResizeObserver: typeof ResizeObserver

//   beforeEach(() => {
//     originalResizeObserver = global.ResizeObserver
//     global.ResizeObserver = vi.fn().mockImplementation((callback) => {
//       mockResizeObserver = new MockResizeObserver(callback)
//       return mockResizeObserver
//     })
//   })

//   afterEach(() => {
//     global.ResizeObserver = originalResizeObserver
//     vi.clearAllMocks()
//   })

//   it('should observe container element when ref is set', () => {
//     const mockSetDimensions = vi.fn()
//     const containerRef = { current: document.createElement('div') }

//     renderHook(() =>
//       useChartResize({
//         setDimensions: mockSetDimensions,
//         containerRef,
//       }),
//     )

//     vExpect(global.ResizeObserver).toHaveBeenCalledWith(expect.any(Function))
//     vExpect(mockResizeObserver.elements.has(containerRef.current)).toBe(true)
//   })

//   it('should not observe when container ref is null', () => {
//     const mockSetDimensions = vi.fn()
//     const containerRef = { current: null }

//     renderHook(() =>
//       useChartResize({
//         setDimensions: mockSetDimensions,
//         containerRef,
//       }),
//     )

//     vExpect(mockResizeObserver.elements.size).toBe(0)
//   })

//   it('should update dimensions when resize is triggered', () => {
//     const mockSetDimensions = vi.fn()
//     const containerRef = { current: document.createElement('div') }

//     renderHook(() =>
//       useChartResize({
//         setDimensions: mockSetDimensions,
//         containerRef,
//       }),
//     )

//     // Simulate resize event
//     const mockEntry = {
//       contentRect: { width: 500, height: 300 },
//       target: containerRef.current,
//     } as ResizeObserverEntry

//     mockResizeObserver.triggerResize([mockEntry])

//     vExpect(mockSetDimensions).toHaveBeenCalledWith(expect.any(Function))

//     // Test the function passed to setDimensions
//     const setDimensionsCall = mockSetDimensions.mock.calls[0][0]
//     const prevDimensions = { width: 100, height: 200 }
//     const newDimensions = setDimensionsCall(prevDimensions)

//     vExpect(newDimensions).toEqual({
//       width: 500,
//       height: 200, // height should remain unchanged
//     })
//   })

//   it('should not update dimensions when no entries', () => {
//     const mockSetDimensions = vi.fn()
//     const containerRef = { current: document.createElement('div') }

//     renderHook(() =>
//       useChartResize({
//         setDimensions: mockSetDimensions,
//         containerRef,
//       }),
//     )

//     // Simulate resize event with no entries
//     mockResizeObserver.triggerResize([])

//     vExpect(mockSetDimensions).not.toHaveBeenCalled()
//   })

//   it('should unobserve element on cleanup', () => {
//     const mockSetDimensions = vi.fn()
//     const containerRef = { current: document.createElement('div') }

//     const { unmount } = renderHook(() =>
//       useChartResize({
//         setDimensions: mockSetDimensions,
//         containerRef,
//       }),
//     )

//     vExpect(mockResizeObserver.elements.has(containerRef.current)).toBe(true)

//     unmount()

//     vExpect(mockResizeObserver.elements.has(containerRef.current)).toBe(false)
//   })

//   it('should handle container ref changing', () => {
//     const mockSetDimensions = vi.fn()
//     let containerRef = { current: document.createElement('div') }

//     const { rerender } = renderHook(
//       ({ ref }) =>
//         useChartResize({
//           setDimensions: mockSetDimensions,
//           containerRef: ref,
//         }),
//       { initialProps: { ref: containerRef } },
//     )

//     const firstElement = containerRef.current
//     vExpect(mockResizeObserver.elements.has(firstElement)).toBe(true)

//     // Change the ref
//     containerRef = { current: document.createElement('div') }
//     rerender({ ref: containerRef })

//     const secondElement = containerRef.current
//     vExpect(mockResizeObserver.elements.has(firstElement)).toBe(false)
//     vExpect(mockResizeObserver.elements.has(secondElement)).toBe(true)
//   })

//   it('should handle setDimensions function changing', () => {
//     const mockSetDimensions1 = vi.fn()
//     const mockSetDimensions2 = vi.fn()
//     const containerRef = { current: document.createElement('div') }

//     const { rerender } = renderHook(
//       ({ setDimensions }) =>
//         useChartResize({
//           setDimensions,
//           containerRef,
//         }),
//       { initialProps: { setDimensions: mockSetDimensions1 } },
//     )

//     // Trigger resize with first function
//     const mockEntry = {
//       contentRect: { width: 500, height: 300 },
//       target: containerRef.current,
//     } as ResizeObserverEntry

//     mockResizeObserver.triggerResize([mockEntry])
//     vExpect(mockSetDimensions1).toHaveBeenCalled()

//     // Change setDimensions function
//     rerender({ setDimensions: mockSetDimensions2 })

//     // Trigger resize with new function
//     mockResizeObserver.triggerResize([mockEntry])
//     vExpect(mockSetDimensions2).toHaveBeenCalled()
//   })
// })
