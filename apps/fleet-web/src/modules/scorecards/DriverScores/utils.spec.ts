import { describe, it, vi } from 'vitest'
import { vExpect } from 'src/vitest/utils'

import {
  transformDriverScoreApiDataToChartData,
  transformApiDataToOverviewChartData,
  bisectDate,
  getSingleDriverPagePath,
  getLineSeries,
  getSafetyScoreSeries,
} from './utils'
import type { DataPoint, LineSeries } from './Overview/types'
import type { FetchDriverScoreData, FetchPeriodGroupScoresData } from './api/queries'
import type { SelectedComparisonGroup } from './types'

// Mock dependencies
vi.mock('src/util-components/ctIntl', () => ({
  ctIntl: {
    formatMessage: vi.fn(({ id }: { id: string }) => id),
  },
}))

vi.mock('api/utils', () => ({
  buildRouteQueryStringKeepingExistingSearchParams: vi.fn(() => 'mocked-query-string'),
}))

vi.mock('./components/utils', () => ({
  calculateChange: vi.fn((current: number, previous: number) => ({
    value: current - previous,
    direction: current > previous ? 'up' : current < previous ? 'down' : 'unchanged',
  })),
}))

describe('DriverScores utils', () => {
  describe('bisectDate', () => {
    const mockData: DataPoint[] = [
      { date: new Date('2024-01-01'), safetyScore: 50 },
      { date: new Date('2024-01-02'), safetyScore: 60 },
      { date: new Date('2024-01-03'), safetyScore: 70 },
      { date: new Date('2024-01-04'), safetyScore: 80 },
    ]

    it('should find correct index for exact date match', () => {
      const targetDate = new Date('2024-01-02')
      const result = bisectDate(mockData, targetDate)
      vExpect(result).toBe(1)
    })

    it('should find correct index for date between points', () => {
      const targetDate = new Date('2024-01-02T12:00:00')
      const result = bisectDate(mockData, targetDate)
      vExpect(result).toBe(1)
    })

    it('should handle date before first point', () => {
      const targetDate = new Date('2023-12-31')
      const result = bisectDate(mockData, targetDate)
      vExpect(result).toBe(0)
    })

    it('should handle date after last point', () => {
      const targetDate = new Date('2024-01-05')
      const result = bisectDate(mockData, targetDate)
      vExpect(result).toBe(3)
    })

    it('should handle empty array', () => {
      const result = bisectDate([], new Date())
      vExpect(result).toBe(0)
    })

    it('should handle custom start and end parameters', () => {
      const targetDate = new Date('2024-01-02')
      const result = bisectDate(mockData, targetDate, 1, 3)
      vExpect(result).toBe(1)
    })
  })

  describe('getSafetyScoreSeries', () => {
    it('should return correct safety score series', () => {
      const color = '#ff0000'
      const result = getSafetyScoreSeries(color)

      vExpect(result.key).toBe('safetyScore')
      vExpect(result.color).toBe(color)
      vExpect(result.dashed).toBe(true)
      vExpect(result.strokeWidth).toBe(1.5)
    })
  })

  describe('getLineSeries', () => {
    const mockComparisonGroups: SelectedComparisonGroup[] = [
      { id: 'group1' as any, name: 'Group 1', color: '#ff0000' },
      { id: 'group2' as any, name: 'Group 2', color: '#00ff00' },
    ]

    it('should return series with safety score when enabled', () => {
      const safetyScore = { color: '#0000ff', value: 50 }
      const result = getLineSeries({ comparisonGroups: mockComparisonGroups, safetyScore })

      vExpect(result.length).toBe(3) // safety score + 2 groups
      vExpect(result[0].key).toBe('safetyScore')
      vExpect(result[1].key).toBe('group_group1')
      vExpect(result[2].key).toBe('group_group2')
    })

    it('should return series without safety score when disabled', () => {
      const safetyScore = { color: '#0000ff', value: null }
      const result = getLineSeries({ comparisonGroups: mockComparisonGroups, safetyScore })

      vExpect(result.length).toBe(2) // only groups
      vExpect(result[0].key).toBe('group_group1')
      vExpect(result[1].key).toBe('group_group2')
    })

    it('should handle empty comparison groups', () => {
      const safetyScore = { color: '#0000ff', value: 50 }
      const result = getLineSeries({ comparisonGroups: [], safetyScore })

      vExpect(result.length).toBe(1) // only safety score
      vExpect(result[0].key).toBe('safetyScore')
    })
  })

  describe('getSingleDriverPagePath', () => {
    it('should build correct path with search params', () => {
      const location = { search: '?existing=param' }
      const searchParams = { driverId: 'driver123' } as any

      const result = getSingleDriverPagePath(location, searchParams)

      vExpect(result).toContain('/driver-scores?')
      vExpect(result).toContain('mocked-query-string')
    })
  })

  describe('transformDriverScoreApiDataToChartData', () => {
    const mockDriver = {
      id: 'driver1' as any,
      name: 'Test Driver',
      color: '#ff0000',
    }

    const mockSafetyScore = {
      color: '#0000ff',
      value: 50,
    }

    const startDate = new Date('2024-01-01')
    const endDate = new Date('2024-01-03')

    it('should handle undefined API data', () => {
      const result = transformDriverScoreApiDataToChartData({
        apiData: undefined,
        startDate,
        endDate,
        driver: mockDriver,
        safetyScore: mockSafetyScore,
      })

      vExpect(result.currentPeriod.score).toBe(0)
      vExpect(result.previousPeriod.score).toBe(0)
      vExpect(result.distanceDriven).toBe(0)
      vExpect(result.timeDriven.hours).toBe(0)
      vExpect(result.timeDriven.minutes).toBe(0)
      vExpect(result.dailyScores.length).toBe(3) // 3 days
    })

    it('should transform valid API data correctly', () => {
      const mockApiData: FetchDriverScoreData['periodScores'][number] = {
        id: 'driver1' as any,
        scores: [80, 85, 90],
        avgScore: 85,
        prevAvgScore: 80,
        timeInSecond: 3600,
        prevTimeInSecond: 3000,
        distanceInKm: 100,
        prevDistanceInKm: 80,
        categories: [],
      }

      const result = transformDriverScoreApiDataToChartData({
        apiData: mockApiData,
        startDate,
        endDate,
        driver: mockDriver,
        safetyScore: mockSafetyScore,
      })

      vExpect(result.currentPeriod.score).toBe(85)
      vExpect(result.previousPeriod.score).toBe(80)
      vExpect(result.distanceDriven).toBe(100)
      vExpect(result.timeDriven.hours).toBe(1)
      vExpect(result.timeDriven.minutes).toBe(0)
      vExpect(result.dailyScores.length).toBe(3)
      vExpect(result.lineSeries.length).toBe(2) // safety score + driver
    })
  })

  describe('transformApiDataToOverviewChartData', () => {
    const mockComparisonGroups: SelectedComparisonGroup[] = [
      { id: 'group1' as any, name: 'Group 1', color: '#ff0000' },
    ]

    const mockSafetyScore = {
      color: '#0000ff',
      value: 50,
    }

    const startDate = new Date('2024-01-01')
    const endDate = new Date('2024-01-02')

    it('should transform API data correctly', () => {
      const mockApiData: FetchPeriodGroupScoresData['periodScores'] = [
        {
          id: 'group1' as any,
          scores: [80, 85],
          avgScore: 82,
          prevAvgScore: 78,
          timeInSecond: 3600,
          prevTimeInSecond: 3000,
          distanceInKm: 100,
          prevDistanceInKm: 80,
          categories: [],
        },
      ]

      const result = transformApiDataToOverviewChartData({
        apiData: mockApiData,
        startDate,
        endDate,
        comparisonGroups: mockComparisonGroups,
        safetyScore: mockSafetyScore,
      })

      vExpect(result.currentPeriod.score).toBe(82)
      vExpect(result.previousPeriod.score).toBe(78)
      vExpect(result.distanceDriven).toBe(100)
      vExpect(result.timeDriven.hours).toBe(1)
      vExpect(result.timeDriven.minutes).toBe(0)
      vExpect(result.dailyScores.length).toBe(2)
      vExpect(result.lineSeries.length).toBe(2) // safety score + group
    })

    it('should handle empty API data', () => {
      const result = transformApiDataToOverviewChartData({
        apiData: [],
        startDate,
        endDate,
        comparisonGroups: mockComparisonGroups,
        safetyScore: mockSafetyScore,
      })

      vExpect(result.currentPeriod.score).toBe(0)
      vExpect(result.previousPeriod.score).toBe(0)
      vExpect(result.distanceDriven).toBe(0)
      vExpect(result.dailyScores.length).toBe(2)
    })
  })
})
