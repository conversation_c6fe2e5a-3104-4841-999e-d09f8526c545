/// <reference types="vitest/config" />
import { defineConfig } from 'vite'
import fs from 'node:fs/promises'
import react from '@vitejs/plugin-react'
import { join } from 'node:path'
import { nxViteTsPaths } from '@nx/vite/plugins/nx-tsconfig-paths.plugin'

import dotenv from 'dotenv'
import { z } from 'zod/v4'
import commonjs from 'vite-plugin-commonjs'
import fixReactVirtualized from 'esbuild-plugin-react-virtualized'
import type { InlineConfig as VitestInlineConfig } from 'vitest/node'

type ViteConfigWithTest = {
  test: VitestInlineConfig
} & ReturnType<typeof defineConfig>

dotenv.config()

// Schema for when we want to run project with vite instead of rspack
const viteSchema = z.object({
  NODE_ENV: z.enum([
    'development',
    'production',
    'cypress-component-test-run',
    'cypress-component-test-open',
  ]),
})

const NODE_ENV = process.env.VITEST
  ? 'development'
  : viteSchema.parse(process.env).NODE_ENV

// https://vitejs.dev/config/
const baseConfig = defineConfig({
  root: __dirname,
  base: '/',
  cacheDir: process.env.VITEST ? '../../node_modules/.vitest' : undefined,
  optimizeDeps: {
    esbuildOptions: {
      plugins: [fixReactVirtualized as any],
    },
  },

  plugins: [
    {
      name: 'svg-inline',
      enforce: 'pre',
      // Handle SVG imports
      load: async (id) => {
        if (id.endsWith('.svg') && id.includes('/assets/')) {
          const file = await fs.readFile(id, 'utf8')
          return `export default ${JSON.stringify(file)}`
        }
        return undefined // do nothing
      },
    },
    react(),
    commonjs(),
    nxViteTsPaths(),
  ],

  build: {
    outDir: join(process.cwd(), 'dist/apps/fleet-web'),
  },

  server: {
    port: 8080,
    host: '0.0.0.0',
  },

  define: {
    ENV: {
      NODE_ENV: NODE_ENV,
      ...(NODE_ENV === 'production'
        ? {
            GA_TRACKING_ID: 'UA-153974304-1',
            GMAP_API_KEY: process.env.STAGING
              ? process.env.STAGING_GMAP_API_KEY
              : process.env.GMAP_API_KEY,
            SENTRY_DSN:
              'https://<EMAIL>/1444444',
          }
        : {
            ENABLE_REDUX_DEV_MIDDLEWARE: process.env.ENABLE_REDUX_DEV_MIDDLEWARE,
            OVERRIDE_ENDPOINT: process.env.OVERRIDE_ENDPOINT,
            GMAP_API_KEY: process.env.GMAP_API_KEY,
          }),
      DEPLOYMENT_ENV: process.env.DEPLOYMENT_ENV || 'unspecified',
      APP_VERSION: process.env.npm_package_version,
      APP_VERSION_WITH_METADATA:
        process.env.npm_package_cartrack_meta_version_with_metadata,
      HERE_MAPS_API_KEY: process.env.HERE_MAPS_API_KEY,
      CYPRESS_CT_ENV:
        NODE_ENV === 'cypress-component-test-open' ||
        NODE_ENV === 'cypress-component-test-run'
          ? NODE_ENV
          : null,
      FEAT_MIFLEET_DECIMAL_SEPARATORS: process.env.FEAT_MIFLEET_DECIMAL_SEPARATORS,
      NEW_LOGIN: process.env.NEW_LOGIN,
    },
  },

  css: {
    preprocessorOptions: {
      scss: {
        javascriptEnabled: true,
      } as any,
    },
  },
})

const fullConfig: ViteConfigWithTest = {
  ...baseConfig,
  test: {
    browser: {
      provider: 'playwright',
      enabled: true,
      name: 'chromium',
      headless: true,
    },
    reporters: ['default'],
    coverage: {
      // include: ['src/util-functions/**/*.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
      // enabled: true,
      reportsDirectory: '../../coverage/apps/fleet-web',
    },
    environment: 'jsdom',
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
  },
}

export default fullConfig
